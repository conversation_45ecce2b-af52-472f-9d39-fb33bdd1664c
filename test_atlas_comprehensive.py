#!/usr/bin/env python3
"""
Comprehensive Test Suite for A.T.L.A.S AI Trading System
Tests all components including real-time data access, trading operations, and AI analysis capabilities
"""

import asyncio
import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Test configuration
BASE_URL = "http://localhost:8080"
TEST_SYMBOLS = ["AAPL", "MSFT", "GOOGL", "TSLA", "SPY"]

class AtlasTestSuite:
    """Comprehensive test suite for A.T.L.A.S AI Trading System"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.test_results = []
        self.failed_tests = []
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        
        if not success:
            self.failed_tests.append(test_name)
    
    def test_system_health(self):
        """Test 1: System Health Check"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                system_name = data.get("system", "")
                status = data.get("status", "")
                
                if "A.T.L.A.S" in system_name and "running" in status.lower():
                    self.log_test("System Health Check", True, f"System: {system_name}, Status: {status}")
                else:
                    self.log_test("System Health Check", False, f"Unexpected response: {data}")
            else:
                self.log_test("System Health Check", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("System Health Check", False, f"Connection error: {str(e)}")
    
    def test_capabilities_endpoint(self):
        """Test 2: Capabilities Endpoint"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/holly/capabilities", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check for key capabilities
                required_fields = ["name", "core_capabilities", "trading_features", "api_integrations"]
                missing_fields = [field for field in required_fields if field not in data]
                
                if not missing_fields:
                    capabilities_count = len(data.get("core_capabilities", []))
                    self.log_test("Capabilities Endpoint", True, f"Found {capabilities_count} core capabilities")
                else:
                    self.log_test("Capabilities Endpoint", False, f"Missing fields: {missing_fields}")
            else:
                self.log_test("Capabilities Endpoint", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Capabilities Endpoint", False, f"Error: {str(e)}")
    
    def test_chat_interface(self):
        """Test 3: Main Chat Interface"""
        try:
            test_message = "Hello A.T.L.A.S! Can you tell me about your trading capabilities?"
            
            response = requests.post(
                f"{self.base_url}/api/v1/holly/chat",
                json={"message": test_message, "user_context": {}},
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if "response" in data and len(data["response"]) > 50:
                    response_type = data.get("type", "unknown")
                    self.log_test("Chat Interface", True, f"Response type: {response_type}, Length: {len(data['response'])}")
                else:
                    self.log_test("Chat Interface", False, f"Invalid response: {data}")
            else:
                self.log_test("Chat Interface", False, f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            self.log_test("Chat Interface", False, f"Error: {str(e)}")
    
    def test_real_time_quote(self):
        """Test 4: Real-Time Quote Access"""
        try:
            test_symbol = "AAPL"
            test_message = f"What's the current price of {test_symbol}?"
            
            response = requests.post(
                f"{self.base_url}/api/v1/holly/chat",
                json={"message": test_message, "user_context": {}},
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                function_called = data.get("function_called")
                
                if function_called == "get_real_time_quote":
                    self.log_test("Real-Time Quote", True, f"Function called: {function_called}")
                else:
                    # Check if response contains price information
                    response_text = data.get("response", "").lower()
                    if "price" in response_text or "$" in response_text:
                        self.log_test("Real-Time Quote", True, "Price information found in response")
                    else:
                        self.log_test("Real-Time Quote", False, f"No price data found. Function: {function_called}")
            else:
                self.log_test("Real-Time Quote", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Real-Time Quote", False, f"Error: {str(e)}")
    
    def test_stock_analysis(self):
        """Test 5: Comprehensive Stock Analysis"""
        try:
            test_symbol = "MSFT"
            test_message = f"Analyze {test_symbol} stock with current market data and technical indicators"
            
            response = requests.post(
                f"{self.base_url}/api/v1/holly/chat",
                json={"message": test_message, "user_context": {}},
                headers={"Content-Type": "application/json"},
                timeout=45
            )
            
            if response.status_code == 200:
                data = response.json()
                function_called = data.get("function_called")
                response_text = data.get("response", "")
                
                # Check for analysis indicators
                analysis_keywords = ["technical", "analysis", "rsi", "macd", "support", "resistance", "trend"]
                found_keywords = [kw for kw in analysis_keywords if kw.lower() in response_text.lower()]
                
                if function_called == "analyze_stock_comprehensive" or len(found_keywords) >= 3:
                    self.log_test("Stock Analysis", True, f"Analysis keywords found: {found_keywords}")
                else:
                    self.log_test("Stock Analysis", False, f"Limited analysis. Function: {function_called}")
            else:
                self.log_test("Stock Analysis", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Stock Analysis", False, f"Error: {str(e)}")
    
    def test_trading_plan_creation(self):
        """Test 6: Trading Plan Creation"""
        try:
            test_message = "Create a trading plan to make $50 today with moderate risk"
            
            response = requests.post(
                f"{self.base_url}/api/v1/holly/chat",
                json={"message": test_message, "user_context": {}},
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                function_called = data.get("function_called")
                requires_action = data.get("requires_action", False)
                
                if function_called == "create_trading_plan" or requires_action:
                    plan_id = data.get("plan_id")
                    if plan_id:
                        self.log_test("Trading Plan Creation", True, f"Plan created with ID: {plan_id}")
                    else:
                        self.log_test("Trading Plan Creation", True, "Trading plan functionality triggered")
                else:
                    # Check response for trading plan content
                    response_text = data.get("response", "").lower()
                    plan_keywords = ["trade", "buy", "sell", "entry", "exit", "stop", "target"]
                    found_keywords = [kw for kw in plan_keywords if kw in response_text]
                    
                    if len(found_keywords) >= 4:
                        self.log_test("Trading Plan Creation", True, f"Trading plan content found: {found_keywords}")
                    else:
                        self.log_test("Trading Plan Creation", False, f"No trading plan generated. Function: {function_called}")
            else:
                self.log_test("Trading Plan Creation", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Trading Plan Creation", False, f"Error: {str(e)}")
    
    def test_sentiment_analysis(self):
        """Test 7: News & Sentiment Analysis"""
        try:
            test_message = "Analyze the current market sentiment for TSLA"
            
            response = requests.post(
                f"{self.base_url}/api/v1/holly/chat",
                json={"message": test_message, "user_context": {}},
                headers={"Content-Type": "application/json"},
                timeout=45
            )
            
            if response.status_code == 200:
                data = response.json()
                function_called = data.get("function_called")
                response_text = data.get("response", "")
                
                # Check for sentiment analysis
                sentiment_keywords = ["sentiment", "bullish", "bearish", "neutral", "news", "positive", "negative"]
                found_keywords = [kw for kw in sentiment_keywords if kw.lower() in response_text.lower()]
                
                if function_called == "analyze_sentiment" or len(found_keywords) >= 2:
                    self.log_test("Sentiment Analysis", True, f"Sentiment analysis found: {found_keywords}")
                else:
                    self.log_test("Sentiment Analysis", False, f"No sentiment analysis. Function: {function_called}")
            else:
                self.log_test("Sentiment Analysis", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Sentiment Analysis", False, f"Error: {str(e)}")
    
    def test_enhanced_api_endpoints(self):
        """Test 8: Enhanced A.T.L.A.S API Endpoints"""
        endpoints_to_test = [
            ("/api/v1/atlas/sentiment/AAPL", "Sentiment API"),
            ("/api/v1/atlas/market-sentiment", "Market Sentiment API"),
            ("/api/v1/atlas/opportunities", "Opportunities API")
        ]
        
        for endpoint, test_name in endpoints_to_test:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        self.log_test(test_name, True, f"Endpoint working: {endpoint}")
                    else:
                        error = data.get("error", "Unknown error")
                        self.log_test(test_name, False, f"API error: {error}")
                elif response.status_code == 503:
                    self.log_test(test_name, True, "Service unavailable (expected in dev mode)")
                else:
                    self.log_test(test_name, False, f"HTTP {response.status_code}")
                    
            except Exception as e:
                self.log_test(test_name, False, f"Error: {str(e)}")
    
    def test_ai_stop_loss(self):
        """Test 9: AI-Enhanced Stop-Loss Calculation"""
        try:
            test_message = "Calculate an AI-enhanced stop-loss for AAPL at $150 entry with 100 shares"
            
            response = requests.post(
                f"{self.base_url}/api/v1/holly/chat",
                json={"message": test_message, "user_context": {}},
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                function_called = data.get("function_called")
                response_text = data.get("response", "")
                
                # Check for stop-loss calculation
                stop_keywords = ["stop", "loss", "risk", "price", "$", "calculate"]
                found_keywords = [kw for kw in stop_keywords if kw.lower() in response_text.lower()]
                
                if function_called == "calculate_ai_stop_loss" or len(found_keywords) >= 4:
                    self.log_test("AI Stop-Loss", True, f"Stop-loss calculation found: {found_keywords}")
                else:
                    self.log_test("AI Stop-Loss", False, f"No stop-loss calculation. Function: {function_called}")
            else:
                self.log_test("AI Stop-Loss", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("AI Stop-Loss", False, f"Error: {str(e)}")
    
    def test_market_intelligence(self):
        """Test 10: Market Intelligence & News Search"""
        try:
            test_message = "Search for latest market news and events affecting tech stocks"
            
            response = requests.post(
                f"{self.base_url}/api/v1/holly/chat",
                json={"message": test_message, "user_context": {}},
                headers={"Content-Type": "application/json"},
                timeout=45
            )
            
            if response.status_code == 200:
                data = response.json()
                function_called = data.get("function_called")
                response_text = data.get("response", "")
                
                # Check for news/market intelligence
                news_keywords = ["news", "market", "events", "tech", "stocks", "latest", "search"]
                found_keywords = [kw for kw in news_keywords if kw.lower() in response_text.lower()]
                
                if function_called == "search_market_news" or len(found_keywords) >= 4:
                    self.log_test("Market Intelligence", True, f"Market intelligence found: {found_keywords}")
                else:
                    self.log_test("Market Intelligence", False, f"Limited market intelligence. Function: {function_called}")
            else:
                self.log_test("Market Intelligence", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("Market Intelligence", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all tests and generate report"""
        print("🚀 Starting A.T.L.A.S AI Trading System Comprehensive Test Suite")
        print("=" * 80)
        
        start_time = time.time()
        
        # Run all tests
        self.test_system_health()
        self.test_capabilities_endpoint()
        self.test_chat_interface()
        self.test_real_time_quote()
        self.test_stock_analysis()
        self.test_trading_plan_creation()
        self.test_sentiment_analysis()
        self.test_enhanced_api_endpoints()
        self.test_ai_stop_loss()
        self.test_market_intelligence()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Generate report
        self.generate_report(duration)
    
    def generate_report(self, duration: float):
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results if t["success"]])
        failed_tests = len(self.failed_tests)
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print("\n" + "=" * 80)
        print("📊 A.T.L.A.S AI Trading System Test Report")
        print("=" * 80)
        print(f"🕒 Test Duration: {duration:.2f} seconds")
        print(f"📈 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📊 Success Rate: {success_rate:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for test_name in self.failed_tests:
                print(f"   • {test_name}")
        
        print(f"\n🎯 System Status: {'OPERATIONAL' if success_rate >= 70 else 'NEEDS ATTENTION'}")
        
        if success_rate >= 90:
            print("🎉 Excellent! A.T.L.A.S AI Trading System is fully operational!")
        elif success_rate >= 70:
            print("✅ Good! A.T.L.A.S AI Trading System is mostly operational with minor issues.")
        else:
            print("⚠️ Warning! A.T.L.A.S AI Trading System needs attention before production use.")
        
        print("\n🔗 Access A.T.L.A.S at: http://localhost:8080")
        print("📚 API Documentation: http://localhost:8080/docs")
        print("=" * 80)


def main():
    """Main test execution"""
    test_suite = AtlasTestSuite()
    test_suite.run_all_tests()


if __name__ == "__main__":
    main()
