#!/usr/bin/env python3
"""
Test script for A.T.L.A.S AI Trading System
Comprehensive testing of enhanced capabilities
"""

import requests
import json
import time
from datetime import datetime

def test_atlas_api():
    """Test A.T.L.A.S API endpoints"""
    base_url = "http://localhost:8080"
    
    print("🚀 Testing A.T.L.A.S AI Trading System")
    print("=" * 60)
    
    # Test 1: Health Check
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            data = response.json()
            print("✅ Health Check:")
            print(f"   Status: {data.get('status')}")
            print(f"   System: {data.get('system')}")
            print(f"   LLM Integration: {data.get('llm_integration')}")
            print(f"   Mode: {data.get('mode')}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    
    print()
    
    # Test 2: Capabilities
    try:
        response = requests.get(f"{base_url}/api/v1/holly/capabilities")
        if response.status_code == 200:
            data = response.json()
            print("✅ A.T.L.A.S Capabilities:")
            print(f"   Name: {data.get('name')}")
            print(f"   Description: {data.get('description')}")
            print(f"   Status: {data.get('status')}")
            print(f"   Services: {data.get('services_status')}")
            
            print("\n   🔌 API Integrations:")
            for api, status in data.get('api_integrations', {}).items():
                print(f"     {api}: {status}")
            
            print("\n   🎯 Core Capabilities:")
            for capability in data.get('core_capabilities', [])[:5]:
                print(f"     • {capability}")
            
            print("\n   💼 Trading Features:")
            for feature in data.get('trading_features', []):
                print(f"     • {feature}")
                
        else:
            print(f"❌ Capabilities test failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Capabilities test error: {e}")
    
    print()
    
    # Test 3: Chat with A.T.L.A.S
    try:
        chat_data = {
            "message": "Hello A.T.L.A.S! Can you tell me about your trading capabilities?",
            "user_context": {}
        }
        
        response = requests.post(
            f"{base_url}/api/v1/holly/chat",
            json=chat_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ A.T.L.A.S Chat Test:")
            print(f"   Response Type: {data.get('type')}")
            print(f"   Function Called: {data.get('function_called', 'None')}")
            print(f"   Timestamp: {data.get('timestamp')}")
            print("\n   🤖 A.T.L.A.S Response:")
            print(f"   {data.get('response', 'No response')[:200]}...")
        else:
            print(f"❌ Chat test failed: {response.status_code}")
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ Chat test error: {e}")
    
    print()
    
    # Test 4: Stock Analysis Request
    try:
        analysis_data = {
            "message": "Can you analyze AAPL stock with current market data?",
            "user_context": {}
        }
        
        response = requests.post(
            f"{base_url}/api/v1/holly/chat",
            json=analysis_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Stock Analysis Test:")
            print(f"   Response Type: {data.get('type')}")
            print(f"   Function Called: {data.get('function_called', 'None')}")
            print(f"   Requires Action: {data.get('requires_action')}")
            print("\n   📊 Analysis Response:")
            print(f"   {data.get('response', 'No response')[:300]}...")
        else:
            print(f"❌ Stock analysis test failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Stock analysis test error: {e}")
    
    print()
    
    # Test 5: Trading Plan Request
    try:
        trading_data = {
            "message": "Create a trading plan to make $50 today",
            "user_context": {}
        }
        
        response = requests.post(
            f"{base_url}/api/v1/holly/chat",
            json=trading_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Trading Plan Test:")
            print(f"   Response Type: {data.get('type')}")
            print(f"   Function Called: {data.get('function_called', 'None')}")
            print(f"   Requires Action: {data.get('requires_action')}")
            if data.get('trading_plan'):
                print(f"   Trading Plan ID: {data.get('plan_id')}")
            print("\n   💰 Trading Plan Response:")
            print(f"   {data.get('response', 'No response')[:300]}...")
        else:
            print(f"❌ Trading plan test failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Trading plan test error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 A.T.L.A.S AI Trading System Test Complete!")
    print("📊 The system is ready for comprehensive trading assistance")
    print("🔗 Access the system at: http://localhost:8080")

if __name__ == "__main__":
    test_atlas_api()
