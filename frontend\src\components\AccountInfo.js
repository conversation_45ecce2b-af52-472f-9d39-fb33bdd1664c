import React from 'react';
import { Card, CardContent, Typography, Grid } from '@mui/material';

const AccountInfo = ({ accountData }) => {
  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Account Information
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography variant="body2" color="textSecondary">
              Portfolio Value
            </Typography>
            <Typography variant="h6">
              ${accountData?.portfolio_value || '0.00'}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="textSecondary">
              Buying Power
            </Typography>
            <Typography variant="h6">
              ${accountData?.buying_power || '0.00'}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="textSecondary">
              Day Trade Count
            </Typography>
            <Typography variant="h6">
              {accountData?.daytrade_count || 0}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="textSecondary">
              Status
            </Typography>
            <Typography variant="h6">
              {accountData?.status || 'ACTIVE'}
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default AccountInfo;
