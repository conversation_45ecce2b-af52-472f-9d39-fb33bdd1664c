import React from 'react';
import { Card, CardContent, Typography } from '@mui/material';

const Dashboard = ({ accountData, positions, signals }) => {
  return (
    <Card>
      <CardContent>
        <Typography variant="h6">Dashboard</Typography>
        <Typography variant="body2">
          Account Value: ${accountData?.portfolio_value || '0.00'}
        </Typography>
        <Typography variant="body2">
          Positions: {positions?.length || 0}
        </Typography>
        <Typography variant="body2">
          Signals: {signals?.length || 0}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default Dashboard;
