{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"action\", \"className\", \"message\", \"role\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { emphasize } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Paper from '../Paper';\nimport { getSnackbarContentUtilityClass } from './snackbarContentClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    action: ['action'],\n    message: ['message']\n  };\n  return composeClasses(slots, getSnackbarContentUtilityClass, classes);\n};\nconst SnackbarContentRoot = styled(Paper, {\n  name: 'MuiSnackbarContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => {\n  const emphasis = theme.palette.mode === 'light' ? 0.8 : 0.98;\n  const backgroundColor = emphasize(theme.palette.background.default, emphasis);\n  return _extends({}, theme.typography.body2, {\n    color: theme.vars ? theme.vars.palette.SnackbarContent.color : theme.palette.getContrastText(backgroundColor),\n    backgroundColor: theme.vars ? theme.vars.palette.SnackbarContent.bg : backgroundColor,\n    display: 'flex',\n    alignItems: 'center',\n    flexWrap: 'wrap',\n    padding: '6px 16px',\n    borderRadius: (theme.vars || theme).shape.borderRadius,\n    flexGrow: 1,\n    [theme.breakpoints.up('sm')]: {\n      flexGrow: 'initial',\n      minWidth: 288\n    }\n  });\n});\nconst SnackbarContentMessage = styled('div', {\n  name: 'MuiSnackbarContent',\n  slot: 'Message',\n  overridesResolver: (props, styles) => styles.message\n})({\n  padding: '8px 0'\n});\nconst SnackbarContentAction = styled('div', {\n  name: 'MuiSnackbarContent',\n  slot: 'Action',\n  overridesResolver: (props, styles) => styles.action\n})({\n  display: 'flex',\n  alignItems: 'center',\n  marginLeft: 'auto',\n  paddingLeft: 16,\n  marginRight: -8\n});\nconst SnackbarContent = /*#__PURE__*/React.forwardRef(function SnackbarContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSnackbarContent'\n  });\n  const {\n      action,\n      className,\n      message,\n      role = 'alert'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(SnackbarContentRoot, _extends({\n    role: role,\n    square: true,\n    elevation: 6,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    children: [/*#__PURE__*/_jsx(SnackbarContentMessage, {\n      className: classes.message,\n      ownerState: ownerState,\n      children: message\n    }), action ? /*#__PURE__*/_jsx(SnackbarContentAction, {\n      className: classes.action,\n      ownerState: ownerState,\n      children: action\n    }) : null]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SnackbarContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The action to display. It renders after the message, at the end of the snackbar.\n   */\n  action: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The message to display.\n   */\n  message: PropTypes.node,\n  /**\n   * The ARIA role attribute of the element.\n   * @default 'alert'\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default SnackbarContent;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "emphasize", "styled", "useDefaultProps", "Paper", "getSnackbarContentUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "slots", "root", "action", "message", "SnackbarContentRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "emphasis", "palette", "mode", "backgroundColor", "background", "default", "typography", "body2", "color", "vars", "SnackbarContent", "getContrastText", "bg", "display", "alignItems", "flexWrap", "padding", "borderRadius", "shape", "flexGrow", "breakpoints", "up", "min<PERSON><PERSON><PERSON>", "SnackbarContentMessage", "SnackbarContentAction", "marginLeft", "paddingLeft", "marginRight", "forwardRef", "inProps", "ref", "className", "role", "other", "square", "elevation", "children", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/material/SnackbarContent/SnackbarContent.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"action\", \"className\", \"message\", \"role\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { emphasize } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Paper from '../Paper';\nimport { getSnackbarContentUtilityClass } from './snackbarContentClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    action: ['action'],\n    message: ['message']\n  };\n  return composeClasses(slots, getSnackbarContentUtilityClass, classes);\n};\nconst SnackbarContentRoot = styled(Paper, {\n  name: 'MuiSnackbarContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => {\n  const emphasis = theme.palette.mode === 'light' ? 0.8 : 0.98;\n  const backgroundColor = emphasize(theme.palette.background.default, emphasis);\n  return _extends({}, theme.typography.body2, {\n    color: theme.vars ? theme.vars.palette.SnackbarContent.color : theme.palette.getContrastText(backgroundColor),\n    backgroundColor: theme.vars ? theme.vars.palette.SnackbarContent.bg : backgroundColor,\n    display: 'flex',\n    alignItems: 'center',\n    flexWrap: 'wrap',\n    padding: '6px 16px',\n    borderRadius: (theme.vars || theme).shape.borderRadius,\n    flexGrow: 1,\n    [theme.breakpoints.up('sm')]: {\n      flexGrow: 'initial',\n      minWidth: 288\n    }\n  });\n});\nconst SnackbarContentMessage = styled('div', {\n  name: 'MuiSnackbarContent',\n  slot: 'Message',\n  overridesResolver: (props, styles) => styles.message\n})({\n  padding: '8px 0'\n});\nconst SnackbarContentAction = styled('div', {\n  name: 'MuiSnackbarContent',\n  slot: 'Action',\n  overridesResolver: (props, styles) => styles.action\n})({\n  display: 'flex',\n  alignItems: 'center',\n  marginLeft: 'auto',\n  paddingLeft: 16,\n  marginRight: -8\n});\nconst SnackbarContent = /*#__PURE__*/React.forwardRef(function SnackbarContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSnackbarContent'\n  });\n  const {\n      action,\n      className,\n      message,\n      role = 'alert'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(SnackbarContentRoot, _extends({\n    role: role,\n    square: true,\n    elevation: 6,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    children: [/*#__PURE__*/_jsx(SnackbarContentMessage, {\n      className: classes.message,\n      ownerState: ownerState,\n      children: message\n    }), action ? /*#__PURE__*/_jsx(SnackbarContentAction, {\n      className: classes.action,\n      ownerState: ownerState,\n      children: action\n    }) : null]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SnackbarContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The action to display. It renders after the message, at the end of the snackbar.\n   */\n  action: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The message to display.\n   */\n  message: PropTypes.node,\n  /**\n   * The ARIA role attribute of the element.\n   * @default 'alert'\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default SnackbarContent;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC;AAC5D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,SAAS,QAAQ,8BAA8B;AACxD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,8BAA8B,QAAQ,0BAA0B;AACzE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,MAAM,EAAE,CAAC,QAAQ,CAAC;IAClBC,OAAO,EAAE,CAAC,SAAS;EACrB,CAAC;EACD,OAAOhB,cAAc,CAACa,KAAK,EAAER,8BAA8B,EAAEO,OAAO,CAAC;AACvE,CAAC;AACD,MAAMK,mBAAmB,GAAGf,MAAM,CAACE,KAAK,EAAE;EACxCc,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFS;AACF,CAAC,KAAK;EACJ,MAAMC,QAAQ,GAAGD,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,OAAO,GAAG,GAAG,GAAG,IAAI;EAC5D,MAAMC,eAAe,GAAG1B,SAAS,CAACsB,KAAK,CAACE,OAAO,CAACG,UAAU,CAACC,OAAO,EAAEL,QAAQ,CAAC;EAC7E,OAAO7B,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,CAACO,UAAU,CAACC,KAAK,EAAE;IAC1CC,KAAK,EAAET,KAAK,CAACU,IAAI,GAAGV,KAAK,CAACU,IAAI,CAACR,OAAO,CAACS,eAAe,CAACF,KAAK,GAAGT,KAAK,CAACE,OAAO,CAACU,eAAe,CAACR,eAAe,CAAC;IAC7GA,eAAe,EAAEJ,KAAK,CAACU,IAAI,GAAGV,KAAK,CAACU,IAAI,CAACR,OAAO,CAACS,eAAe,CAACE,EAAE,GAAGT,eAAe;IACrFU,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,CAAClB,KAAK,CAACU,IAAI,IAAIV,KAAK,EAAEmB,KAAK,CAACD,YAAY;IACtDE,QAAQ,EAAE,CAAC;IACX,CAACpB,KAAK,CAACqB,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC5BF,QAAQ,EAAE,SAAS;MACnBG,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,MAAMC,sBAAsB,GAAG7C,MAAM,CAAC,KAAK,EAAE;EAC3CgB,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,SAAS;EACfC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACDwB,OAAO,EAAE;AACX,CAAC,CAAC;AACF,MAAMQ,qBAAqB,GAAG9C,MAAM,CAAC,KAAK,EAAE;EAC1CgB,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC;EACDsB,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBW,UAAU,EAAE,MAAM;EAClBC,WAAW,EAAE,EAAE;EACfC,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;AACF,MAAMjB,eAAe,GAAG,aAAarC,KAAK,CAACuD,UAAU,CAAC,SAASlB,eAAeA,CAACmB,OAAO,EAAEC,GAAG,EAAE;EAC3F,MAAMjC,KAAK,GAAGlB,eAAe,CAAC;IAC5BkB,KAAK,EAAEgC,OAAO;IACdnC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFH,MAAM;MACNwC,SAAS;MACTvC,OAAO;MACPwC,IAAI,GAAG;IACT,CAAC,GAAGnC,KAAK;IACToC,KAAK,GAAG/D,6BAA6B,CAAC2B,KAAK,EAAEzB,SAAS,CAAC;EACzD,MAAMe,UAAU,GAAGU,KAAK;EACxB,MAAMT,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,KAAK,CAACQ,mBAAmB,EAAEtB,QAAQ,CAAC;IACtD6D,IAAI,EAAEA,IAAI;IACVE,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,CAAC;IACZJ,SAAS,EAAExD,IAAI,CAACa,OAAO,CAACE,IAAI,EAAEyC,SAAS,CAAC;IACxC5C,UAAU,EAAEA,UAAU;IACtB2C,GAAG,EAAEA;EACP,CAAC,EAAEG,KAAK,EAAE;IACRG,QAAQ,EAAE,CAAC,aAAarD,IAAI,CAACwC,sBAAsB,EAAE;MACnDQ,SAAS,EAAE3C,OAAO,CAACI,OAAO;MAC1BL,UAAU,EAAEA,UAAU;MACtBiD,QAAQ,EAAE5C;IACZ,CAAC,CAAC,EAAED,MAAM,GAAG,aAAaR,IAAI,CAACyC,qBAAqB,EAAE;MACpDO,SAAS,EAAE3C,OAAO,CAACG,MAAM;MACzBJ,UAAU,EAAEA,UAAU;MACtBiD,QAAQ,EAAE7C;IACZ,CAAC,CAAC,GAAG,IAAI;EACX,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF8C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7B,eAAe,CAAC8B,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;EACEjD,MAAM,EAAEjB,SAAS,CAACmE,IAAI;EACtB;AACF;AACA;EACErD,OAAO,EAAEd,SAAS,CAACoE,MAAM;EACzB;AACF;AACA;EACEX,SAAS,EAAEzD,SAAS,CAACqE,MAAM;EAC3B;AACF;AACA;EACEnD,OAAO,EAAElB,SAAS,CAACmE,IAAI;EACvB;AACF;AACA;AACA;EACET,IAAI,EAAE1D,SAAS,CAAC,sCAAsCqE,MAAM;EAC5D;AACF;AACA;EACEC,EAAE,EAAEtE,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAACwE,OAAO,CAACxE,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAACoE,MAAM,EAAEpE,SAAS,CAAC0E,IAAI,CAAC,CAAC,CAAC,EAAE1E,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAACoE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAehC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}