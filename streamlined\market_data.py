"""
Streamlined A.T.L.A.S Trading System - Market Data Service
Consolidated market data, news, and web search functionality
"""

import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

from .config import settings, get_api_headers, get_api_url, validate_symbol, validate_timeframe
from .models import Quote, OHLCV, NewsArticle, SentimentAnalysis


class MarketDataService:
    """Consolidated market data service with real-time quotes, historical data, news, and web search"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session: Optional[aiohttp.ClientSession] = None
        self.cache: Dict[str, Any] = {}
        self.cache_ttl = settings.CACHE_TTL
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=settings.API_TIMEOUT))
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def get_real_time_quote(self, symbol: str) -> Quote:
        """Get real-time stock quote with <2 second latency requirement"""
        symbol = validate_symbol(symbol)
        cache_key = f"quote_{symbol}"
        
        # Check cache first (30 second TTL for quotes)
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if (datetime.now() - timestamp).seconds < 30:
                return cached_data
        
        try:
            # Try Alpaca first (faster for real-time)
            quote = await self._get_alpaca_quote(symbol)
            if quote:
                self.cache[cache_key] = (quote, datetime.now())
                return quote
                
            # Fallback to FMP
            quote = await self._get_fmp_quote(symbol)
            if quote:
                self.cache[cache_key] = (quote, datetime.now())
                return quote
                
            raise Exception("No quote data available")
            
        except Exception as e:
            self.logger.error(f"Error getting quote for {symbol}: {e}")
            raise
    
    async def _get_alpaca_quote(self, symbol: str) -> Optional[Quote]:
        """Get quote from Alpaca API"""
        try:
            url = get_api_url("alpaca", "quotes", symbol=symbol)
            headers = get_api_headers("alpaca")
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    quote_data = data.get("quote", {})
                    
                    return Quote(
                        symbol=symbol,
                        price=float(quote_data.get("ap", 0)),  # Ask price as current
                        change=0,  # Calculate from previous close
                        change_percent=0,
                        volume=int(quote_data.get("as", 0)),
                        timestamp=datetime.now(),
                        bid=float(quote_data.get("bp", 0)),
                        ask=float(quote_data.get("ap", 0))
                    )
        except Exception as e:
            self.logger.warning(f"Alpaca quote failed for {symbol}: {e}")
            return None
    
    async def _get_fmp_quote(self, symbol: str) -> Optional[Quote]:
        """Get quote from FMP API"""
        try:
            url = get_api_url("fmp", "quote", symbol=symbol)
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    if data and len(data) > 0:
                        quote_data = data[0]
                        
                        return Quote(
                            symbol=symbol,
                            price=float(quote_data.get("price", 0)),
                            change=float(quote_data.get("change", 0)),
                            change_percent=float(quote_data.get("changesPercentage", 0)),
                            volume=int(quote_data.get("volume", 0)),
                            timestamp=datetime.now(),
                            high=float(quote_data.get("dayHigh", 0)),
                            low=float(quote_data.get("dayLow", 0)),
                            open=float(quote_data.get("open", 0))
                        )
        except Exception as e:
            self.logger.warning(f"FMP quote failed for {symbol}: {e}")
            return None
    
    async def get_historical_data(self, symbol: str, timeframe: str, limit: int = 100) -> List[OHLCV]:
        """Get historical OHLCV data for charting"""
        symbol = validate_symbol(symbol)
        timeframe = validate_timeframe(timeframe)
        cache_key = f"historical_{symbol}_{timeframe}_{limit}"
        
        # Check cache (5 minute TTL for historical data)
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if (datetime.now() - timestamp).seconds < 300:
                return cached_data
        
        try:
            # Try Alpaca first for intraday data
            if timeframe != "1Day":
                data = await self._get_alpaca_bars(symbol, timeframe, limit)
            else:
                data = await self._get_fmp_historical(symbol, limit)
            
            if data:
                self.cache[cache_key] = (data, datetime.now())
                return data
                
            return []
            
        except Exception as e:
            self.logger.error(f"Error getting historical data for {symbol}: {e}")
            return []
    
    async def _get_alpaca_bars(self, symbol: str, timeframe: str, limit: int) -> List[OHLCV]:
        """Get bars from Alpaca API"""
        try:
            # Convert timeframe to Alpaca format
            alpaca_timeframe = timeframe.replace("Min", "min").replace("Hour", "hour")
            
            url = get_api_url("alpaca", "bars", symbol=symbol)
            url += f"?timeframe={alpaca_timeframe}&limit={limit}&asof=2024-01-01"
            headers = get_api_headers("alpaca")
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    bars = data.get("bars", [])
                    
                    return [
                        OHLCV(
                            timestamp=datetime.fromisoformat(bar["t"].replace("Z", "+00:00")),
                            open=float(bar["o"]),
                            high=float(bar["h"]),
                            low=float(bar["l"]),
                            close=float(bar["c"]),
                            volume=int(bar["v"]),
                            symbol=symbol
                        )
                        for bar in bars
                    ]
        except Exception as e:
            self.logger.warning(f"Alpaca bars failed for {symbol}: {e}")
            return []
    
    async def _get_fmp_historical(self, symbol: str, limit: int) -> List[OHLCV]:
        """Get historical data from FMP API"""
        try:
            url = get_api_url("fmp", "historical", symbol=symbol)
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    historical = data.get("historical", [])[:limit]
                    
                    return [
                        OHLCV(
                            timestamp=datetime.strptime(bar["date"], "%Y-%m-%d"),
                            open=float(bar["open"]),
                            high=float(bar["high"]),
                            low=float(bar["low"]),
                            close=float(bar["close"]),
                            volume=int(bar["volume"]),
                            symbol=symbol
                        )
                        for bar in historical
                    ]
        except Exception as e:
            self.logger.warning(f"FMP historical failed for {symbol}: {e}")
            return []
    
    async def get_market_news(self, symbol: Optional[str] = None, limit: int = 10) -> List[NewsArticle]:
        """Get market news with sentiment analysis"""
        cache_key = f"news_{symbol or 'general'}_{limit}"
        
        # Check cache (5 minute TTL for news)
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if (datetime.now() - timestamp).seconds < 300:
                return cached_data
        
        try:
            url = get_api_url("fmp", "news")
            if symbol:
                url += f"&tickers={symbol}"
            url += f"&limit={limit}"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    articles = []
                    for article in data[:limit]:
                        news_article = NewsArticle(
                            title=article.get("title", ""),
                            content=article.get("text", ""),
                            url=article.get("url", ""),
                            source=article.get("site", ""),
                            published_at=datetime.fromisoformat(
                                article.get("publishedDate", "").replace("Z", "+00:00")
                            ),
                            symbols=[symbol] if symbol else []
                        )
                        
                        # Add basic sentiment scoring
                        news_article.sentiment_score = self._calculate_sentiment_score(
                            news_article.title + " " + (news_article.content or "")
                        )
                        
                        articles.append(news_article)
                    
                    self.cache[cache_key] = (articles, datetime.now())
                    return articles
                    
        except Exception as e:
            self.logger.error(f"Error getting news: {e}")
            return []
    
    def _calculate_sentiment_score(self, text: str) -> float:
        """Basic sentiment scoring using keyword matching"""
        from .config import BULLISH_KEYWORDS, BEARISH_KEYWORDS
        
        text_lower = text.lower()
        bullish_count = sum(1 for keyword in BULLISH_KEYWORDS if keyword in text_lower)
        bearish_count = sum(1 for keyword in BEARISH_KEYWORDS if keyword in text_lower)
        
        total_keywords = bullish_count + bearish_count
        if total_keywords == 0:
            return 0.0
        
        # Score from -1 (bearish) to +1 (bullish)
        return (bullish_count - bearish_count) / total_keywords
    
    async def search_web(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search web for additional market information"""
        try:
            if settings.GOOGLE_SEARCH_API_KEY and settings.GOOGLE_SEARCH_ENGINE_ID:
                return await self._google_search(query, limit)
            else:
                # Fallback to news search
                return await self._fallback_search(query, limit)
                
        except Exception as e:
            self.logger.error(f"Web search failed: {e}")
            return []
    
    async def _google_search(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Google Custom Search API"""
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                "key": settings.GOOGLE_SEARCH_API_KEY,
                "cx": settings.GOOGLE_SEARCH_ENGINE_ID,
                "q": query,
                "num": limit
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    items = data.get("items", [])
                    
                    return [
                        {
                            "title": item.get("title", ""),
                            "snippet": item.get("snippet", ""),
                            "url": item.get("link", ""),
                            "source": "Google Search"
                        }
                        for item in items
                    ]
        except Exception as e:
            self.logger.warning(f"Google search failed: {e}")
            return []
    
    async def _fallback_search(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Fallback search using news API"""
        try:
            # Use news search as fallback
            articles = await self.get_market_news(limit=limit)
            
            # Filter articles that match query
            relevant_articles = []
            query_lower = query.lower()
            
            for article in articles:
                if (query_lower in article.title.lower() or 
                    query_lower in (article.content or "").lower()):
                    relevant_articles.append({
                        "title": article.title,
                        "snippet": article.content[:200] if article.content else "",
                        "url": article.url,
                        "source": article.source
                    })
            
            return relevant_articles[:limit]
            
        except Exception as e:
            self.logger.warning(f"Fallback search failed: {e}")
            return []
    
    async def get_multiple_quotes(self, symbols: List[str]) -> Dict[str, Quote]:
        """Get quotes for multiple symbols efficiently"""
        tasks = [self.get_real_time_quote(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        quotes = {}
        for symbol, result in zip(symbols, results):
            if isinstance(result, Quote):
                quotes[symbol] = result
            else:
                self.logger.warning(f"Failed to get quote for {symbol}: {result}")
        
        return quotes
    
    async def stream_price_updates(self, symbols: List[str], callback):
        """Stream real-time price updates (simplified implementation)"""
        while True:
            try:
                quotes = await self.get_multiple_quotes(symbols)
                await callback(quotes)
                await asyncio.sleep(1)  # Update every second
            except Exception as e:
                self.logger.error(f"Error in price stream: {e}")
                await asyncio.sleep(5)  # Wait before retry

    async def get_enhanced_market_context(self, symbol: str, timeframe: str = "1Day") -> Dict[str, Any]:
        """Get comprehensive market context with real-time signals"""
        try:
            # Get current quote and historical data
            quote = await self.get_real_time_quote(symbol)
            historical_data = await self.get_historical_data(symbol, timeframe, 50)
            news = await self.get_market_news(symbol, limit=5)

            if not historical_data or len(historical_data) < 20:
                return {"error": "Insufficient historical data"}

            # Calculate technical indicators
            from .technical_analysis import TechnicalAnalysisEngine
            ta_engine = TechnicalAnalysisEngine()
            indicators = ta_engine.calculate_indicators(historical_data)

            # Calculate additional context signals
            context = {
                "symbol": symbol,
                "current_price": quote.price,
                "price_change": quote.change,
                "price_change_percent": quote.change_percent,
                "volume": quote.volume,
                "timestamp": quote.timestamp.isoformat(),

                # Technical indicators
                "rsi": indicators.rsi,
                "macd": indicators.macd,
                "macd_signal": indicators.macd_signal,
                "macd_histogram": indicators.macd_histogram,
                "bb_upper": indicators.bb_upper,
                "bb_middle": indicators.bb_middle,
                "bb_lower": indicators.bb_lower,
                "sma_20": indicators.sma_20,
                "ema_12": indicators.ema_12,
                "ema_26": indicators.ema_26,
                "atr": indicators.atr,
                "volume_sma": indicators.volume_sma,

                # Derived signals
                "volume_ratio": quote.volume / indicators.volume_sma if indicators.volume_sma else 0,
                "price_vs_sma20": ((quote.price - indicators.sma_20) / indicators.sma_20 * 100) if indicators.sma_20 else 0,
                "bb_position": self._calculate_bb_position(quote.price, indicators),
                "trend_direction": self._determine_trend_direction(indicators),
                "momentum_direction": self._determine_momentum_direction(indicators),

                # Support/Resistance levels
                "support_level": self._calculate_support_level(historical_data),
                "resistance_level": self._calculate_resistance_level(historical_data),

                # TTM Squeeze detection
                "ttm_squeeze": self._detect_ttm_squeeze(indicators),

                # EMA alignment
                "ema8": self._calculate_ema(historical_data, 8),
                "ema21": self._calculate_ema(historical_data, 21),
                "ema_alignment": self._check_ema_alignment(historical_data),

                # News sentiment
                "news_sentiment": self._analyze_news_sentiment(news),
                "news_count": len(news)
            }

            return context

        except Exception as e:
            self.logger.error(f"Error getting enhanced market context for {symbol}: {e}")
            return {"error": str(e)}

    def _calculate_bb_position(self, price: float, indicators) -> str:
        """Calculate Bollinger Band position"""
        if not (indicators.bb_upper and indicators.bb_lower):
            return "unknown"

        if price > indicators.bb_upper:
            return "above_upper"
        elif price < indicators.bb_lower:
            return "below_lower"
        elif price > indicators.bb_middle:
            return "upper_half"
        else:
            return "lower_half"

    def _determine_trend_direction(self, indicators) -> str:
        """Determine overall trend direction"""
        if not (indicators.ema_12 and indicators.ema_26):
            return "unknown"

        if indicators.ema_12 > indicators.ema_26:
            return "up"
        elif indicators.ema_12 < indicators.ema_26:
            return "down"
        else:
            return "sideways"

    def _determine_momentum_direction(self, indicators) -> str:
        """Determine momentum direction from MACD"""
        if not indicators.macd_histogram:
            return "unknown"

        if indicators.macd_histogram > 0:
            return "up"
        elif indicators.macd_histogram < 0:
            return "down"
        else:
            return "neutral"

    def _calculate_support_level(self, historical_data: List) -> Optional[float]:
        """Calculate recent support level"""
        if len(historical_data) < 20:
            return None

        recent_lows = [bar.low for bar in historical_data[-20:]]
        return min(recent_lows)

    def _calculate_resistance_level(self, historical_data: List) -> Optional[float]:
        """Calculate recent resistance level"""
        if len(historical_data) < 20:
            return None

        recent_highs = [bar.high for bar in historical_data[-20:]]
        return max(recent_highs)

    def _detect_ttm_squeeze(self, indicators) -> bool:
        """Detect TTM Squeeze condition"""
        if not (indicators.bb_upper and indicators.bb_lower and indicators.atr):
            return False

        # Simplified TTM Squeeze: BB width vs ATR-based Keltner Channel width
        bb_width = indicators.bb_upper - indicators.bb_lower
        kc_width = indicators.atr * 3  # Simplified Keltner Channel width

        return bb_width < kc_width

    def _calculate_ema(self, historical_data: List, period: int) -> Optional[float]:
        """Calculate EMA for given period"""
        if len(historical_data) < period:
            return None

        closes = [bar.close for bar in historical_data[-period:]]
        multiplier = 2 / (period + 1)
        ema = closes[0]

        for price in closes[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))

        return ema

    def _check_ema_alignment(self, historical_data: List) -> str:
        """Check EMA alignment for trend confirmation"""
        ema8 = self._calculate_ema(historical_data, 8)
        ema21 = self._calculate_ema(historical_data, 21)

        if not (ema8 and ema21):
            return "unknown"

        if ema8 > ema21:
            return "bullish"
        elif ema8 < ema21:
            return "bearish"
        else:
            return "neutral"

    def _analyze_news_sentiment(self, news: List) -> Dict[str, Any]:
        """Analyze overall news sentiment"""
        if not news:
            return {"overall": "neutral", "score": 0.0, "confidence": 0.0}

        scores = [article.sentiment_score for article in news if article.sentiment_score is not None]

        if not scores:
            return {"overall": "neutral", "score": 0.0, "confidence": 0.0}

        avg_score = sum(scores) / len(scores)

        if avg_score > 0.2:
            overall = "positive"
        elif avg_score < -0.2:
            overall = "negative"
        else:
            overall = "neutral"

        return {
            "overall": overall,
            "score": avg_score,
            "confidence": min(len(scores) / 5.0, 1.0),  # Higher confidence with more articles
            "article_count": len(scores)
        }

    async def explain_price_movement(self, symbol: str, timeframe: str = "1Day") -> Dict[str, Any]:
        """Explain recent price movement using enhanced market context"""
        try:
            # Get enhanced context instead of basic data
            context = await self.get_enhanced_market_context(symbol, timeframe)

            if "error" in context:
                return context

            # Generate comprehensive explanation
            explanation = self._generate_enhanced_movement_explanation(context)

            return {
                "symbol": symbol,
                "movement": {
                    "direction": "up" if context["price_change"] > 0 else "down",
                    "magnitude": abs(context["price_change_percent"]),
                    "price": context["current_price"],
                    "change": context["price_change"]
                },
                "technical_context": {
                    "rsi": context.get("rsi"),
                    "trend_direction": context.get("trend_direction"),
                    "momentum_direction": context.get("momentum_direction"),
                    "volume_ratio": context.get("volume_ratio"),
                    "bb_position": context.get("bb_position"),
                    "ttm_squeeze": context.get("ttm_squeeze")
                },
                "news_analysis": context.get("news_sentiment", {}),
                "explanation": explanation,
                "full_context": context
            }

        except Exception as e:
            self.logger.error(f"Error explaining price movement for {symbol}: {e}")
            return {"error": str(e)}

    def _generate_enhanced_movement_explanation(self, context: Dict[str, Any]) -> str:
        """Generate enhanced explanation using full market context"""
        symbol = context["symbol"]
        direction = "up" if context["price_change"] > 0 else "down"
        magnitude = abs(context["price_change_percent"])

        explanation = f"{symbol} is {direction} {magnitude:.2f}% today. "

        # Technical analysis context
        if context.get("rsi"):
            rsi = context["rsi"]
            if rsi > 70:
                explanation += f"RSI at {rsi:.1f} indicates overbought conditions. "
            elif rsi < 30:
                explanation += f"RSI at {rsi:.1f} suggests oversold conditions. "

        # Trend context
        trend = context.get("trend_direction")
        if trend == "up":
            explanation += "The stock is in an uptrend with EMAs aligned bullishly. "
        elif trend == "down":
            explanation += "The stock is in a downtrend with bearish EMA alignment. "

        # Volume context
        volume_ratio = context.get("volume_ratio", 0)
        if volume_ratio > 2:
            explanation += f"High volume ({volume_ratio:.1f}x average) confirms the move. "
        elif volume_ratio < 0.5:
            explanation += f"Low volume ({volume_ratio:.1f}x average) suggests weak conviction. "

        # TTM Squeeze context
        if context.get("ttm_squeeze"):
            explanation += "TTM Squeeze is active - expect volatility expansion soon. "

        # News sentiment
        news_sentiment = context.get("news_sentiment", {})
        if news_sentiment.get("overall") == "positive":
            explanation += "Recent news sentiment is positive, supporting the move. "
        elif news_sentiment.get("overall") == "negative":
            explanation += "Negative news sentiment may be contributing to the decline. "

        return explanation

    def _generate_movement_explanation(self, quote: Quote, news: List[Dict]) -> str:
        """Generate explanation for price movement"""
        direction = "up" if quote.change > 0 else "down"
        magnitude = abs(quote.change_percent)

        explanation = f"{quote.symbol} is {direction} {magnitude:.2f}% today. "

        if news:
            sentiment_avg = sum(n["sentiment"] for n in news) / len(news)
            if sentiment_avg > 0.2:
                explanation += "Recent positive news sentiment may be driving the upward movement. "
            elif sentiment_avg < -0.2:
                explanation += "Recent negative news sentiment may be contributing to the decline. "

        if magnitude > 5:
            explanation += "This is a significant move that warrants attention. "
        elif magnitude < 1:
            explanation += "This is normal daily volatility. "

        return explanation
