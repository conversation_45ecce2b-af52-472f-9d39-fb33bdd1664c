"""
Enhanced A.T.L.A.S Trading System - Feedback and Learning System
Memory persistence and user feedback collection for continuous improvement
"""

import asyncio
import json
import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import hashlib

from .models import ChatMess<PERSON>, AIResponse
from .config import settings


class FeedbackSystem:
    """System for collecting user feedback and learning from interactions"""
    
    def __init__(self, db_path: str = "atlas_feedback.db"):
        self.logger = logging.getLogger(__name__)
        self.db_path = db_path
        self.conversation_cache = []
        self.feedback_cache = []
        
        # Initialize database
        self._init_database()
        
        # Load recent conversations into memory
        self._load_recent_conversations()
    
    def _init_database(self):
        """Initialize SQLite database for persistent storage"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Conversations table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS conversations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        message_id TEXT UNIQUE NOT NULL,
                        role TEXT NOT NULL,
                        content TEXT NOT NULL,
                        response_type TEXT,
                        function_called TEXT,
                        context TEXT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Feedback table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS feedback (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        message_id TEXT NOT NULL,
                        feedback_type TEXT NOT NULL,
                        rating INTEGER,
                        comment TEXT,
                        improvement_suggestion TEXT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (message_id) REFERENCES conversations (message_id)
                    )
                """)
                
                # Response quality metrics
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS response_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        message_id TEXT NOT NULL,
                        response_time REAL,
                        accuracy_score REAL,
                        helpfulness_score REAL,
                        relevance_score REAL,
                        validation_passed BOOLEAN,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (message_id) REFERENCES conversations (message_id)
                    )
                """)
                
                # Learning patterns table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS learning_patterns (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        pattern_type TEXT NOT NULL,
                        pattern_data TEXT NOT NULL,
                        success_rate REAL,
                        usage_count INTEGER DEFAULT 1,
                        last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                conn.commit()
                self.logger.info("Feedback database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Error initializing feedback database: {e}")
    
    def _load_recent_conversations(self, days: int = 7):
        """Load recent conversations into memory for quick access"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = datetime.now() - timedelta(days=days)
                cursor.execute("""
                    SELECT message_id, role, content, response_type, function_called, 
                           context, timestamp
                    FROM conversations 
                    WHERE timestamp > ? 
                    ORDER BY timestamp DESC
                    LIMIT 1000
                """, (cutoff_date,))
                
                rows = cursor.fetchall()
                self.conversation_cache = [
                    {
                        'message_id': row[0],
                        'role': row[1],
                        'content': row[2],
                        'response_type': row[3],
                        'function_called': row[4],
                        'context': json.loads(row[5]) if row[5] else {},
                        'timestamp': datetime.fromisoformat(row[6])
                    }
                    for row in rows
                ]
                
                self.logger.info(f"Loaded {len(self.conversation_cache)} recent conversations")
                
        except Exception as e:
            self.logger.error(f"Error loading recent conversations: {e}")
    
    async def store_conversation(self, session_id: str, message: ChatMessage, 
                                response: AIResponse, context: Dict[str, Any] = None) -> str:
        """Store conversation message and response"""
        try:
            message_id = self._generate_message_id(message.content, message.timestamp)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Store user message
                cursor.execute("""
                    INSERT OR REPLACE INTO conversations 
                    (session_id, message_id, role, content, context, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    session_id,
                    f"{message_id}_user",
                    message.role,
                    message.content,
                    json.dumps(context or {}),
                    message.timestamp.isoformat()
                ))
                
                # Store assistant response
                cursor.execute("""
                    INSERT OR REPLACE INTO conversations 
                    (session_id, message_id, role, content, response_type, function_called, context, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session_id,
                    f"{message_id}_assistant",
                    "assistant",
                    response.response,
                    response.type,
                    response.function_called,
                    json.dumps(context or {}),
                    response.timestamp.isoformat()
                ))
                
                conn.commit()
            
            # Add to cache
            self.conversation_cache.extend([
                {
                    'message_id': f"{message_id}_user",
                    'role': message.role,
                    'content': message.content,
                    'context': context or {},
                    'timestamp': message.timestamp
                },
                {
                    'message_id': f"{message_id}_assistant",
                    'role': "assistant",
                    'content': response.response,
                    'response_type': response.type,
                    'function_called': response.function_called,
                    'context': context or {},
                    'timestamp': response.timestamp
                }
            ])
            
            return message_id
            
        except Exception as e:
            self.logger.error(f"Error storing conversation: {e}")
            return ""
    
    async def collect_feedback(self, message_id: str, feedback_type: str, 
                              rating: Optional[int] = None, comment: str = "",
                              improvement_suggestion: str = "") -> bool:
        """Collect user feedback on AI responses"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO feedback 
                    (message_id, feedback_type, rating, comment, improvement_suggestion)
                    VALUES (?, ?, ?, ?, ?)
                """, (message_id, feedback_type, rating, comment, improvement_suggestion))
                
                conn.commit()
            
            # Add to cache
            self.feedback_cache.append({
                'message_id': message_id,
                'feedback_type': feedback_type,
                'rating': rating,
                'comment': comment,
                'improvement_suggestion': improvement_suggestion,
                'timestamp': datetime.now()
            })
            
            self.logger.info(f"Collected feedback for message {message_id}: {feedback_type}")
            
            # Trigger learning update if enough feedback collected
            if len(self.feedback_cache) % 10 == 0:
                await self._update_learning_patterns()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error collecting feedback: {e}")
            return False
    
    async def store_response_metrics(self, message_id: str, response_time: float,
                                   accuracy_score: float = None, helpfulness_score: float = None,
                                   relevance_score: float = None, validation_passed: bool = None) -> bool:
        """Store response quality metrics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO response_metrics 
                    (message_id, response_time, accuracy_score, helpfulness_score, 
                     relevance_score, validation_passed)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (message_id, response_time, accuracy_score, helpfulness_score,
                      relevance_score, validation_passed))
                
                conn.commit()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error storing response metrics: {e}")
            return False
    
    async def _update_learning_patterns(self):
        """Update learning patterns based on feedback"""
        try:
            # Analyze successful response patterns
            successful_patterns = self._analyze_successful_patterns()
            
            # Update pattern database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for pattern_type, pattern_data in successful_patterns.items():
                    pattern_json = json.dumps(pattern_data)
                    pattern_hash = hashlib.md5(pattern_json.encode()).hexdigest()
                    
                    # Check if pattern exists
                    cursor.execute("""
                        SELECT id, usage_count FROM learning_patterns 
                        WHERE pattern_type = ? AND pattern_data = ?
                    """, (pattern_type, pattern_json))
                    
                    existing = cursor.fetchone()
                    
                    if existing:
                        # Update usage count
                        cursor.execute("""
                            UPDATE learning_patterns 
                            SET usage_count = usage_count + 1, last_used = CURRENT_TIMESTAMP
                            WHERE id = ?
                        """, (existing[0],))
                    else:
                        # Insert new pattern
                        cursor.execute("""
                            INSERT INTO learning_patterns 
                            (pattern_type, pattern_data, success_rate)
                            VALUES (?, ?, ?)
                        """, (pattern_type, pattern_json, pattern_data.get('success_rate', 0.5)))
                
                conn.commit()
            
            self.logger.info("Updated learning patterns based on recent feedback")
            
        except Exception as e:
            self.logger.error(f"Error updating learning patterns: {e}")
    
    def _analyze_successful_patterns(self) -> Dict[str, Any]:
        """Analyze patterns from successful interactions"""
        patterns = {}
        
        try:
            # Get recent positive feedback
            positive_feedback = [f for f in self.feedback_cache 
                               if f['rating'] and f['rating'] >= 4]
            
            if not positive_feedback:
                return patterns
            
            # Analyze response types that get good feedback
            response_type_success = {}
            for feedback in positive_feedback:
                message_id = feedback['message_id']
                
                # Find corresponding conversation
                conv = next((c for c in self.conversation_cache 
                           if c['message_id'] == message_id), None)
                
                if conv and conv.get('response_type'):
                    response_type = conv['response_type']
                    if response_type not in response_type_success:
                        response_type_success[response_type] = {'count': 0, 'total_rating': 0}
                    
                    response_type_success[response_type]['count'] += 1
                    response_type_success[response_type]['total_rating'] += feedback['rating']
            
            # Calculate success rates
            for response_type, data in response_type_success.items():
                if data['count'] > 0:
                    avg_rating = data['total_rating'] / data['count']
                    patterns[f"response_type_{response_type}"] = {
                        'type': response_type,
                        'success_rate': avg_rating / 5.0,  # Normalize to 0-1
                        'sample_size': data['count']
                    }
            
            # Analyze successful query patterns
            successful_queries = []
            for feedback in positive_feedback:
                message_id = feedback['message_id'].replace('_assistant', '_user')
                user_msg = next((c for c in self.conversation_cache 
                               if c['message_id'] == message_id), None)
                
                if user_msg:
                    successful_queries.append(user_msg['content'])
            
            # Extract common keywords from successful queries
            if successful_queries:
                patterns['successful_query_keywords'] = self._extract_keywords(successful_queries)
            
        except Exception as e:
            self.logger.error(f"Error analyzing successful patterns: {e}")
        
        return patterns
    
    def _extract_keywords(self, queries: List[str]) -> Dict[str, Any]:
        """Extract common keywords from successful queries"""
        word_freq = {}
        
        for query in queries:
            words = query.lower().split()
            for word in words:
                if len(word) > 3 and word.isalpha():  # Filter short words and non-alphabetic
                    word_freq[word] = word_freq.get(word, 0) + 1
        
        # Get top keywords
        top_keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20]
        
        return {
            'keywords': [kw[0] for kw in top_keywords],
            'frequencies': dict(top_keywords),
            'total_queries': len(queries)
        }
    
    def _generate_message_id(self, content: str, timestamp: datetime) -> str:
        """Generate unique message ID"""
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        time_hash = hashlib.md5(timestamp.isoformat().encode()).hexdigest()[:8]
        return f"{content_hash}_{time_hash}"
    
    async def get_conversation_history(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get conversation history for a session"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT message_id, role, content, response_type, function_called, 
                           context, timestamp
                    FROM conversations 
                    WHERE session_id = ? 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                """, (session_id, limit))
                
                rows = cursor.fetchall()
                return [
                    {
                        'message_id': row[0],
                        'role': row[1],
                        'content': row[2],
                        'response_type': row[3],
                        'function_called': row[4],
                        'context': json.loads(row[5]) if row[5] else {},
                        'timestamp': row[6]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting conversation history: {e}")
            return []
    
    async def get_feedback_summary(self, days: int = 30) -> Dict[str, Any]:
        """Get feedback summary for the last N days"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Overall feedback stats
                cursor.execute("""
                    SELECT feedback_type, AVG(rating), COUNT(*) 
                    FROM feedback 
                    WHERE timestamp > ? AND rating IS NOT NULL
                    GROUP BY feedback_type
                """, (cutoff_date,))
                
                feedback_stats = {}
                for row in cursor.fetchall():
                    feedback_stats[row[0]] = {
                        'avg_rating': round(row[1], 2),
                        'count': row[2]
                    }
                
                # Response time metrics
                cursor.execute("""
                    SELECT AVG(response_time), MIN(response_time), MAX(response_time)
                    FROM response_metrics 
                    WHERE timestamp > ?
                """, (cutoff_date,))
                
                time_stats = cursor.fetchone()
                
                return {
                    'feedback_stats': feedback_stats,
                    'response_time': {
                        'avg': round(time_stats[0], 2) if time_stats[0] else 0,
                        'min': round(time_stats[1], 2) if time_stats[1] else 0,
                        'max': round(time_stats[2], 2) if time_stats[2] else 0
                    },
                    'period_days': days,
                    'generated_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Error getting feedback summary: {e}")
            return {}
    
    async def get_learning_insights(self) -> Dict[str, Any]:
        """Get insights from learning patterns"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT pattern_type, pattern_data, success_rate, usage_count
                    FROM learning_patterns 
                    ORDER BY success_rate DESC, usage_count DESC
                    LIMIT 20
                """)
                
                patterns = []
                for row in cursor.fetchall():
                    patterns.append({
                        'type': row[0],
                        'data': json.loads(row[1]),
                        'success_rate': row[2],
                        'usage_count': row[3]
                    })
                
                return {
                    'top_patterns': patterns,
                    'total_patterns': len(patterns),
                    'generated_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Error getting learning insights: {e}")
            return {}
    
    async def cleanup_old_data(self, days: int = 90):
        """Clean up old conversation and feedback data"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Delete old conversations
                cursor.execute("DELETE FROM conversations WHERE timestamp < ?", (cutoff_date,))
                conversations_deleted = cursor.rowcount
                
                # Delete old feedback
                cursor.execute("DELETE FROM feedback WHERE timestamp < ?", (cutoff_date,))
                feedback_deleted = cursor.rowcount
                
                # Delete old metrics
                cursor.execute("DELETE FROM response_metrics WHERE timestamp < ?", (cutoff_date,))
                metrics_deleted = cursor.rowcount
                
                conn.commit()
            
            self.logger.info(f"Cleaned up old data: {conversations_deleted} conversations, "
                           f"{feedback_deleted} feedback, {metrics_deleted} metrics")
            
            # Refresh cache
            self._load_recent_conversations()
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")
