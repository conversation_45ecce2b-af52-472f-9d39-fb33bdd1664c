"""
Streamlined A.T.L.A.S Trading System - Trading Book Content
Curated content from top trading books for RAG education system
"""

from typing import Dict, List
from .models import BookContent

# Trading in the Zone - Mark Douglas (Enhanced Content)
TRADING_IN_THE_ZONE_CONTENT = {
    "psychology": [
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 1",
            section="The Psychology of Trading",
            content="""The market is a reflection of mass psychology. Every price movement represents the collective beliefs, expectations, and emotions of all market participants at that moment. Understanding this psychological foundation is crucial for successful trading.

The biggest challenge traders face is not learning to analyze markets, but learning to think in probabilities and manage their emotions. Most traders fail because they approach the market with the wrong mindset - they want certainty in an uncertain environment.

To be consistently successful, you must learn to think like a casino. Casinos know that each individual bet is random, but over a series of bets, the odds are in their favor. Similarly, each trade is uncertain, but with proper risk management and a statistical edge, you can be profitable over time.

PRACTICAL APPLICATION: Before each trade, ask yourself: 'What is my edge?' and 'Am I thinking in probabilities or trying to predict this single outcome?' This mental check prevents emotional decision-making and keeps you focused on your systematic approach.""",
            concepts=["psychology", "mass_psychology", "emotions", "probability_thinking", "uncertainty", "edge", "systematic_approach"]
        ),
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 1",
            section="The Fundamental vs Technical Mindset",
            content="""Most traders get trapped in analysis paralysis because they believe more information equals better trades. This is fundamentally wrong. The market discounts all available information instantly. What matters is not what you know, but how you think about what you know.

Technical analysis works not because patterns predict the future, but because they reveal the collective psychology of market participants. When you see a head and shoulders pattern, you're seeing the emotional journey of traders from optimism to fear to capitulation.

The key insight: Markets are not logical, they are psychological. Price movements reflect human emotions more than fundamental values. This is why a stock can drop 20% on 'good' earnings - the market had already priced in even better expectations.""",
            concepts=["technical_analysis", "market_psychology", "information_processing", "patterns", "expectations", "price_action"]
        ),
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 2", 
            section="Fundamental vs Technical Analysis",
            content="""The market discounts everything. All fundamental information - earnings, economic data, news events - is already reflected in price. What moves markets is not the news itself, but how traders interpret and react to that news.

Technical analysis works because it reveals the collective psychology of market participants. Chart patterns and indicators show us how traders are thinking and feeling about a stock or market. When you understand this, you can anticipate how they might react to future price movements.

The key insight is that markets are driven by human emotions - fear and greed. These emotions create predictable patterns that repeat over time. Technical analysis helps us identify these patterns and profit from them.""",
            concepts=["technical_analysis", "market_psychology", "price_action", "patterns", "emotions"]
        )
    ],
    "discipline": [
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 3",
            section="Developing Discipline",
            content="""Consistency comes from developing a trading mindset that accepts uncertainty and focuses on process over outcomes. Most traders sabotage themselves by focusing on individual trades rather than their long-term edge.

Discipline means following your trading plan regardless of how you feel about a particular trade. It means taking every setup that meets your criteria, cutting losses quickly, and letting winners run. This requires emotional detachment from individual outcomes.

The disciplined trader understands that losses are simply the cost of doing business. They don't take losses personally or let them affect future decisions. Instead, they view each trade as one in a series of trades where their edge will play out over time.

PRACTICAL DISCIPLINE CHECKLIST:
1. Pre-market: Review your trading plan and rules
2. During market: Follow rules mechanically, no exceptions
3. Post-market: Journal what you did right and wrong
4. Weekly: Review performance and adjust rules if needed
5. Monthly: Analyze emotional patterns and triggers

DISCIPLINE MANTRAS:
- 'I trade my plan, not my emotions'
- 'This trade doesn't define me'
- 'Losses are tuition for market education'
- 'My edge plays out over many trades, not one'""",
            concepts=["discipline", "consistency", "trading_plan", "emotional_detachment", "process_focus", "journaling", "mantras", "rules"]
        ),
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 4",
            section="The Mechanics of Discipline",
            content="""True discipline in trading comes from understanding that you have complete control over your actions, but no control over market outcomes. This paradox is what separates successful traders from the rest.

The five fundamental truths of trading:
1. Anything can happen in the market
2. You don't need to know what will happen to make money
3. There is a random distribution between wins and losses
4. An edge is nothing more than a higher probability of one outcome over another
5. Every moment in the market is unique

When you truly accept these truths, you stop trying to predict and start focusing on executing your edge consistently. This is when trading becomes mechanical and emotions become irrelevant.""",
            concepts=["discipline_mechanics", "fundamental_truths", "edge_execution", "randomness", "probability", "mechanical_trading"]
        )
    ],
    "risk_management": [
        BookContent(
            book_title="Trading in the Zone",
            chapter="Chapter 4",
            section="Risk and Money Management",
            content="""Every trade has an uncertain outcome. Before entering any position, you must define your risk and accept that you could lose that amount. This pre-acceptance of risk eliminates the emotional stress that causes poor decision-making.

Position sizing is more important than entry technique. Never risk more than you can afford to lose on any single trade. Most successful traders risk no more than 1-2% of their account on any individual position.

The goal is not to be right on every trade, but to make money over a series of trades. This requires cutting losses short and letting profits run. Your winners must be bigger than your losers for you to be profitable long-term.""",
            concepts=["risk_management", "position_sizing", "loss_acceptance", "money_management", "risk_reward"]
        )
    ]
}

# Market Wizards - Jack Schwager (Enhanced Content)
MARKET_WIZARDS_CONTENT = {
    "interviews": [
        BookContent(
            book_title="Market Wizards",
            chapter="Chapter 1",
            section="Ed Seykota Interview - Risk Management Master",
            content="""Ed Seykota emphasizes that successful trading is about managing risk, not predicting markets. His key insights include: 'The elements of good trading are cutting losses, cutting losses, and cutting losses. If you can follow these three rules, you may have a chance.'

Seykota believes that everyone gets what they want from the market. If you're losing money, examine what you might unconsciously want from trading. Many people use trading to punish themselves or to experience excitement rather than to make money.

The trend is your friend until it bends. Seykota's approach focuses on riding long-term trends and cutting losses quickly when trends change. He emphasizes that the market will teach you everything you need to know if you listen to it.

SEYKOTA'S PRACTICAL RULES:
1. Cut losses immediately when trend changes
2. Position size based on volatility (risk parity)
3. Never add to losing positions
4. Let profits run until trend reversal
5. Trade with the trend, never against it
6. Use stops religiously - no exceptions
7. Focus on process, not profits

SEYKOTA'S TREND FOLLOWING SYSTEM:
- Entry: 20-day breakout above previous high
- Exit: 10-day breakout below previous low
- Position size: 2% risk per trade based on ATR
- No fundamental analysis - pure price action""",
            concepts=["risk_management", "trend_following", "psychology", "loss_cutting", "market_listening", "breakout_system", "position_sizing", "atr"]
        ),
        BookContent(
            book_title="Market Wizards", 
            chapter="Chapter 2",
            section="Richard Dennis Interview",
            content="""Richard Dennis proved that trading can be taught through his famous Turtle experiment. He took novice traders and taught them a simple trend-following system, demonstrating that success comes from following rules, not from innate talent.

The Turtle system was based on breakouts - buying when prices broke above recent highs and selling when they broke below recent lows. The key was position sizing based on volatility and strict adherence to the rules.

Dennis emphasized that most people lose money trading because they can't follow rules consistently. They let emotions override their system. The successful Turtles were those who could mechanically follow the system without second-guessing it.""",
            concepts=["trend_following", "breakouts", "position_sizing", "rule_following", "systematic_trading"]
        )
    ],
    "strategies": [
        BookContent(
            book_title="Market Wizards",
            chapter="Chapter 3", 
            section="Common Trading Strategies",
            content="""The most successful traders in Market Wizards used different approaches, but they shared common principles: they all had a systematic approach, they managed risk carefully, and they were disciplined in following their methods.

Trend followers like Seykota and Dennis focused on catching major moves and riding them for months or years. They accepted many small losses in exchange for occasional large profits. Their systems were designed to capture the few big moves that generated most of their profits.

Contrarian traders looked for extreme sentiment readings and bet against the crowd. They understood that markets often overreact to news, creating opportunities for those willing to take the other side of emotional trades.""",
            concepts=["systematic_trading", "trend_following", "contrarian_trading", "risk_management", "discipline"]
        )
    ]
}

# Technical Analysis Explained - Martin Pring
TECHNICAL_ANALYSIS_EXPLAINED_CONTENT = {
    "charts": [
        BookContent(
            book_title="Technical Analysis Explained",
            chapter="Chapter 1",
            section="Chart Basics",
            content="""Charts are visual representations of the battle between buyers and sellers. Every price movement reflects the changing balance of supply and demand. Understanding this basic concept is fundamental to reading charts effectively.

Support and resistance levels represent areas where the balance between buyers and sellers has shifted in the past. Support is a price level where buying interest has previously emerged, while resistance is where selling pressure has appeared.

Volume confirms price movements. When prices move on high volume, it suggests strong conviction behind the move. When prices move on low volume, the move may lack staying power and could easily reverse.""",
            concepts=["charts", "support_resistance", "supply_demand", "volume", "price_action"]
        )
    ],
    "patterns": [
        BookContent(
            book_title="Technical Analysis Explained",
            chapter="Chapter 2",
            section="Chart Patterns",
            content="""Chart patterns are formations that tend to repeat because they reflect consistent human behavior. Head and shoulders patterns, triangles, and double tops/bottoms are examples of how traders' emotions create recognizable formations.

Continuation patterns like flags and pennants suggest that the current trend will resume after a brief pause. Reversal patterns like head and shoulders suggest that the trend is changing direction.

The key to using patterns successfully is to wait for confirmation. A pattern is not complete until price breaks out of the formation with volume. Many apparent patterns fail, so patience and confirmation are essential.""",
            concepts=["chart_patterns", "continuation_patterns", "reversal_patterns", "confirmation", "breakouts"]
        )
    ],
    "indicators": [
        BookContent(
            book_title="Technical Analysis Explained",
            chapter="Chapter 3",
            section="Technical Indicators",
            content="""Technical indicators are mathematical calculations based on price and volume data. They help identify trends, momentum, and potential reversal points. However, indicators should supplement, not replace, basic chart analysis.

Momentum indicators like RSI and MACD help identify when a trend is losing steam. When price makes new highs but momentum indicators don't, it suggests the trend may be weakening (divergence).

Moving averages smooth out price action and help identify the underlying trend. When price is above the moving average, the trend is up. When below, the trend is down. Crossovers of different moving averages can signal trend changes.""",
            concepts=["technical_indicators", "momentum", "divergence", "moving_averages", "trend_identification"]
        )
    ]
}

# How to Make Money in Stocks - William O'Neil
HOW_TO_MAKE_MONEY_CONTENT = {
    "can_slim": [
        BookContent(
            book_title="How to Make Money in Stocks",
            chapter="Chapter 1",
            section="CAN SLIM System",
            content="""The CAN SLIM system identifies growth stocks with the highest probability of significant price appreciation. Each letter represents a key characteristic to look for:

C - Current quarterly earnings should be up 25% or more
A - Annual earnings growth should be 25% or more for the last 3 years  
N - New products, services, management, or price highs
S - Supply and demand - look for stocks with small float and institutional buying
L - Leader or laggard - buy the leaders in strong industry groups
I - Institutional sponsorship - funds should be buying the stock
M - Market direction - only buy when the general market is in an uptrend

This system combines fundamental analysis (earnings growth) with technical analysis (price action and volume) to identify the best growth stocks.""",
            concepts=["can_slim", "growth_stocks", "earnings_growth", "institutional_buying", "market_direction"]
        )
    ],
    "breakouts": [
        BookContent(
            book_title="How to Make Money in Stocks",
            chapter="Chapter 2",
            section="Breakout Patterns",
            content="""The best time to buy a growth stock is when it breaks out of a proper base pattern on heavy volume. A base is a sideways price consolidation that lasts at least 7-8 weeks and corrects 20-50% from the prior high.

Cup-with-handle patterns are among the most reliable. The cup forms as the stock declines and then recovers to near the old high. The handle is a smaller decline that shakes out weak holders before the breakout.

Volume should increase significantly on the breakout day - at least 50% above average. This confirms that institutions are accumulating the stock. Without volume confirmation, many breakouts fail.""",
            concepts=["breakouts", "base_patterns", "cup_with_handle", "volume_confirmation", "institutional_accumulation"]
        )
    ]
}

# The New Trading for a Living - Alexander Elder
NEW_TRADING_FOR_LIVING_CONTENT = {
    "triple_screen": [
        BookContent(
            book_title="The New Trading for a Living",
            chapter="Chapter 1",
            section="Triple Screen Trading System",
            content="""The Triple Screen system uses three different timeframes to analyze trades. This multi-timeframe approach helps identify high-probability setups by aligning short-term trades with longer-term trends.

Screen 1: Use weekly charts to identify the long-term trend with trend-following indicators like MACD or moving averages.

Screen 2: Use daily charts to find entry points against the weekly trend. Look for pullbacks in uptrends or rallies in downtrends.

Screen 3: Use intraday charts to fine-tune entry and exit points. Enter when the daily chart gives a signal in the direction of the weekly trend.

This system helps avoid fighting the major trend while finding good entry points during temporary counter-trend moves.""",
            concepts=["triple_screen", "multiple_timeframes", "trend_analysis", "entry_timing", "pullbacks"]
        )
    ],
    "psychology": [
        BookContent(
            book_title="The New Trading for a Living",
            chapter="Chapter 2",
            section="Trading Psychology",
            content="""Successful trading requires mastering your emotions. Fear and greed are the two main emotions that destroy trading accounts. Fear prevents you from taking good trades, while greed makes you hold losing positions too long.

Keep a trading diary to track not just your trades, but your emotions and decision-making process. Review your diary regularly to identify patterns in your behavior that lead to losses.

The goal is to become emotionally neutral about individual trades. Each trade is just one in a series. Your edge comes from following your system consistently over many trades, not from being right on any particular trade.""",
            concepts=["trading_psychology", "fear_greed", "trading_diary", "emotional_neutrality", "consistency"]
        )
    ]
}

# Consolidated book content dictionary
ALL_BOOK_CONTENT = {
    "trading_in_the_zone": TRADING_IN_THE_ZONE_CONTENT,
    "market_wizards": MARKET_WIZARDS_CONTENT, 
    "technical_analysis_explained": TECHNICAL_ANALYSIS_EXPLAINED_CONTENT,
    "how_to_make_money_in_stocks": HOW_TO_MAKE_MONEY_CONTENT,
    "new_trading_for_living": NEW_TRADING_FOR_LIVING_CONTENT
}

def get_all_book_content() -> List[BookContent]:
    """Get all book content as a flat list for embedding"""
    all_content = []
    
    for book_key, book_data in ALL_BOOK_CONTENT.items():
        for section_key, section_content in book_data.items():
            if isinstance(section_content, list):
                all_content.extend(section_content)
            else:
                all_content.append(section_content)
    
    return all_content

def search_book_content(query: str, book_filter: str = None) -> List[BookContent]:
    """Search book content by keywords"""
    query_lower = query.lower()
    results = []
    
    for book_key, book_data in ALL_BOOK_CONTENT.items():
        if book_filter and book_filter.lower() not in book_key.lower():
            continue
            
        for section_key, section_content in book_data.items():
            if isinstance(section_content, list):
                for content in section_content:
                    if (query_lower in content.content.lower() or 
                        any(concept.lower() in query_lower for concept in content.concepts)):
                        results.append(content)
    
    return results
