{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"disableGutters\", \"fixed\", \"maxWidth\", \"classes\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport useThemePropsSystem from '../useThemeProps';\nimport systemStyled from '../styled';\nimport createTheme from '../createTheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultTheme = createTheme();\nconst defaultCreateStyledComponent = systemStyled('div', {\n  name: '<PERSON><PERSON><PERSON>ontainer',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n  }\n});\nconst useThemePropsDefault = inProps => useThemePropsSystem({\n  props: inProps,\n  name: 'MuiContainer',\n  defaultTheme\n});\nconst useUtilityClasses = (ownerState, componentName) => {\n  const getContainerUtilityClass = slot => {\n    return generateUtilityClass(componentName, slot);\n  };\n  const {\n    classes,\n    fixed,\n    disableGutters,\n    maxWidth\n  } = ownerState;\n  const slots = {\n    root: ['root', maxWidth && `maxWidth${capitalize(String(maxWidth))}`, fixed && 'fixed', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getContainerUtilityClass, classes);\n};\nexport default function createContainer(options = {}) {\n  const {\n    // This will allow adding custom styled fn (for example for custom sx style function)\n    createStyledComponent = defaultCreateStyledComponent,\n    useThemeProps = useThemePropsDefault,\n    componentName = 'MuiContainer'\n  } = options;\n  const ContainerRoot = createStyledComponent(({\n    theme,\n    ownerState\n  }) => _extends({\n    width: '100%',\n    marginLeft: 'auto',\n    boxSizing: 'border-box',\n    marginRight: 'auto',\n    display: 'block'\n  }, !ownerState.disableGutters && {\n    paddingLeft: theme.spacing(2),\n    paddingRight: theme.spacing(2),\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up('sm')]: {\n      paddingLeft: theme.spacing(3),\n      paddingRight: theme.spacing(3)\n    }\n  }), ({\n    theme,\n    ownerState\n  }) => ownerState.fixed && Object.keys(theme.breakpoints.values).reduce((acc, breakpointValueKey) => {\n    const breakpoint = breakpointValueKey;\n    const value = theme.breakpoints.values[breakpoint];\n    if (value !== 0) {\n      // @ts-ignore\n      acc[theme.breakpoints.up(breakpoint)] = {\n        maxWidth: `${value}${theme.breakpoints.unit}`\n      };\n    }\n    return acc;\n  }, {}), ({\n    theme,\n    ownerState\n  }) => _extends({}, ownerState.maxWidth === 'xs' && {\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up('xs')]: {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      maxWidth: Math.max(theme.breakpoints.values.xs, 444)\n    }\n  }, ownerState.maxWidth &&\n  // @ts-ignore module augmentation fails if custom breakpoints are used\n  ownerState.maxWidth !== 'xs' && {\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up(ownerState.maxWidth)]: {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      maxWidth: `${theme.breakpoints.values[ownerState.maxWidth]}${theme.breakpoints.unit}`\n    }\n  }));\n  const Container = /*#__PURE__*/React.forwardRef(function Container(inProps, ref) {\n    const props = useThemeProps(inProps);\n    const {\n        className,\n        component = 'div',\n        disableGutters = false,\n        fixed = false,\n        maxWidth = 'lg'\n      } = props,\n      other = _objectWithoutPropertiesLoose(props, _excluded);\n    const ownerState = _extends({}, props, {\n      component,\n      disableGutters,\n      fixed,\n      maxWidth\n    });\n\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    const classes = useUtilityClasses(ownerState, componentName);\n    return (/*#__PURE__*/\n      // @ts-ignore theme is injected by the styled util\n      _jsx(ContainerRoot, _extends({\n        as: component\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        ,\n\n        ownerState: ownerState,\n        className: clsx(classes.root, className),\n        ref: ref\n      }, other))\n    );\n  });\n  process.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n    children: PropTypes.node,\n    classes: PropTypes.object,\n    className: PropTypes.string,\n    component: PropTypes.elementType,\n    disableGutters: PropTypes.bool,\n    fixed: PropTypes.bool,\n    maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n  } : void 0;\n  return Container;\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "generateUtilityClass", "composeClasses", "capitalize", "useThemePropsSystem", "systemStyled", "createTheme", "jsx", "_jsx", "defaultTheme", "defaultCreateStyledComponent", "name", "slot", "overridesResolver", "props", "styles", "ownerState", "root", "String", "max<PERSON><PERSON><PERSON>", "fixed", "disableGutters", "useThemePropsDefault", "inProps", "useUtilityClasses", "componentName", "getContainerUtilityClass", "classes", "slots", "createContainer", "options", "createStyledComponent", "useThemeProps", "ContainerRoot", "theme", "width", "marginLeft", "boxSizing", "marginRight", "display", "paddingLeft", "spacing", "paddingRight", "breakpoints", "up", "Object", "keys", "values", "reduce", "acc", "breakpoint<PERSON><PERSON><PERSON><PERSON><PERSON>", "breakpoint", "value", "unit", "Math", "max", "xs", "Container", "forwardRef", "ref", "className", "component", "other", "as", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "elementType", "bool", "oneOfType", "oneOf", "sx", "arrayOf", "func"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/@mui/system/esm/Container/createContainer.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"disableGutters\", \"fixed\", \"maxWidth\", \"classes\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport useThemePropsSystem from '../useThemeProps';\nimport systemStyled from '../styled';\nimport createTheme from '../createTheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultTheme = createTheme();\nconst defaultCreateStyledComponent = systemStyled('div', {\n  name: '<PERSON><PERSON><PERSON>ontainer',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n  }\n});\nconst useThemePropsDefault = inProps => useThemePropsSystem({\n  props: inProps,\n  name: 'MuiContainer',\n  defaultTheme\n});\nconst useUtilityClasses = (ownerState, componentName) => {\n  const getContainerUtilityClass = slot => {\n    return generateUtilityClass(componentName, slot);\n  };\n  const {\n    classes,\n    fixed,\n    disableGutters,\n    maxWidth\n  } = ownerState;\n  const slots = {\n    root: ['root', maxWidth && `maxWidth${capitalize(String(maxWidth))}`, fixed && 'fixed', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getContainerUtilityClass, classes);\n};\nexport default function createContainer(options = {}) {\n  const {\n    // This will allow adding custom styled fn (for example for custom sx style function)\n    createStyledComponent = defaultCreateStyledComponent,\n    useThemeProps = useThemePropsDefault,\n    componentName = 'MuiContainer'\n  } = options;\n  const ContainerRoot = createStyledComponent(({\n    theme,\n    ownerState\n  }) => _extends({\n    width: '100%',\n    marginLeft: 'auto',\n    boxSizing: 'border-box',\n    marginRight: 'auto',\n    display: 'block'\n  }, !ownerState.disableGutters && {\n    paddingLeft: theme.spacing(2),\n    paddingRight: theme.spacing(2),\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up('sm')]: {\n      paddingLeft: theme.spacing(3),\n      paddingRight: theme.spacing(3)\n    }\n  }), ({\n    theme,\n    ownerState\n  }) => ownerState.fixed && Object.keys(theme.breakpoints.values).reduce((acc, breakpointValueKey) => {\n    const breakpoint = breakpointValueKey;\n    const value = theme.breakpoints.values[breakpoint];\n    if (value !== 0) {\n      // @ts-ignore\n      acc[theme.breakpoints.up(breakpoint)] = {\n        maxWidth: `${value}${theme.breakpoints.unit}`\n      };\n    }\n    return acc;\n  }, {}), ({\n    theme,\n    ownerState\n  }) => _extends({}, ownerState.maxWidth === 'xs' && {\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up('xs')]: {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      maxWidth: Math.max(theme.breakpoints.values.xs, 444)\n    }\n  }, ownerState.maxWidth &&\n  // @ts-ignore module augmentation fails if custom breakpoints are used\n  ownerState.maxWidth !== 'xs' && {\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up(ownerState.maxWidth)]: {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      maxWidth: `${theme.breakpoints.values[ownerState.maxWidth]}${theme.breakpoints.unit}`\n    }\n  }));\n  const Container = /*#__PURE__*/React.forwardRef(function Container(inProps, ref) {\n    const props = useThemeProps(inProps);\n    const {\n        className,\n        component = 'div',\n        disableGutters = false,\n        fixed = false,\n        maxWidth = 'lg'\n      } = props,\n      other = _objectWithoutPropertiesLoose(props, _excluded);\n    const ownerState = _extends({}, props, {\n      component,\n      disableGutters,\n      fixed,\n      maxWidth\n    });\n\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    const classes = useUtilityClasses(ownerState, componentName);\n    return (\n      /*#__PURE__*/\n      // @ts-ignore theme is injected by the styled util\n      _jsx(ContainerRoot, _extends({\n        as: component\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        ,\n        ownerState: ownerState,\n        className: clsx(classes.root, className),\n        ref: ref\n      }, other))\n    );\n  });\n  process.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n    children: PropTypes.node,\n    classes: PropTypes.object,\n    className: PropTypes.string,\n    component: PropTypes.elementType,\n    disableGutters: PropTypes.bool,\n    fixed: PropTypes.bool,\n    maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n  } : void 0;\n  return Container;\n}"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC;AAC9F,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,mBAAmB,MAAM,kBAAkB;AAClD,OAAOC,YAAY,MAAM,WAAW;AACpC,OAAOC,WAAW,MAAM,gBAAgB;AACxC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAGH,WAAW,CAAC,CAAC;AAClC,MAAMI,4BAA4B,GAAGL,YAAY,CAAC,KAAK,EAAE;EACvDM,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJC;IACF,CAAC,GAAGF,KAAK;IACT,OAAO,CAACC,MAAM,CAACE,IAAI,EAAEF,MAAM,CAAC,WAAWZ,UAAU,CAACe,MAAM,CAACF,UAAU,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEH,UAAU,CAACI,KAAK,IAAIL,MAAM,CAACK,KAAK,EAAEJ,UAAU,CAACK,cAAc,IAAIN,MAAM,CAACM,cAAc,CAAC;EAC1K;AACF,CAAC,CAAC;AACF,MAAMC,oBAAoB,GAAGC,OAAO,IAAInB,mBAAmB,CAAC;EAC1DU,KAAK,EAAES,OAAO;EACdZ,IAAI,EAAE,cAAc;EACpBF;AACF,CAAC,CAAC;AACF,MAAMe,iBAAiB,GAAGA,CAACR,UAAU,EAAES,aAAa,KAAK;EACvD,MAAMC,wBAAwB,GAAGd,IAAI,IAAI;IACvC,OAAOX,oBAAoB,CAACwB,aAAa,EAAEb,IAAI,CAAC;EAClD,CAAC;EACD,MAAM;IACJe,OAAO;IACPP,KAAK;IACLC,cAAc;IACdF;EACF,CAAC,GAAGH,UAAU;EACd,MAAMY,KAAK,GAAG;IACZX,IAAI,EAAE,CAAC,MAAM,EAAEE,QAAQ,IAAI,WAAWhB,UAAU,CAACe,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,EAAEC,KAAK,IAAI,OAAO,EAAEC,cAAc,IAAI,gBAAgB;EAC5H,CAAC;EACD,OAAOnB,cAAc,CAAC0B,KAAK,EAAEF,wBAAwB,EAAEC,OAAO,CAAC;AACjE,CAAC;AACD,eAAe,SAASE,eAAeA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;EACpD,MAAM;IACJ;IACAC,qBAAqB,GAAGrB,4BAA4B;IACpDsB,aAAa,GAAGV,oBAAoB;IACpCG,aAAa,GAAG;EAClB,CAAC,GAAGK,OAAO;EACX,MAAMG,aAAa,GAAGF,qBAAqB,CAAC,CAAC;IAC3CG,KAAK;IACLlB;EACF,CAAC,KAAKpB,QAAQ,CAAC;IACbuC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE,MAAM;IAClBC,SAAS,EAAE,YAAY;IACvBC,WAAW,EAAE,MAAM;IACnBC,OAAO,EAAE;EACX,CAAC,EAAE,CAACvB,UAAU,CAACK,cAAc,IAAI;IAC/BmB,WAAW,EAAEN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;IAC7BC,YAAY,EAAER,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;IAC9B;IACA,CAACP,KAAK,CAACS,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC5BJ,WAAW,EAAEN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;MAC7BC,YAAY,EAAER,KAAK,CAACO,OAAO,CAAC,CAAC;IAC/B;EACF,CAAC,CAAC,EAAE,CAAC;IACHP,KAAK;IACLlB;EACF,CAAC,KAAKA,UAAU,CAACI,KAAK,IAAIyB,MAAM,CAACC,IAAI,CAACZ,KAAK,CAACS,WAAW,CAACI,MAAM,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,kBAAkB,KAAK;IAClG,MAAMC,UAAU,GAAGD,kBAAkB;IACrC,MAAME,KAAK,GAAGlB,KAAK,CAACS,WAAW,CAACI,MAAM,CAACI,UAAU,CAAC;IAClD,IAAIC,KAAK,KAAK,CAAC,EAAE;MACf;MACAH,GAAG,CAACf,KAAK,CAACS,WAAW,CAACC,EAAE,CAACO,UAAU,CAAC,CAAC,GAAG;QACtChC,QAAQ,EAAE,GAAGiC,KAAK,GAAGlB,KAAK,CAACS,WAAW,CAACU,IAAI;MAC7C,CAAC;IACH;IACA,OAAOJ,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACPf,KAAK;IACLlB;EACF,CAAC,KAAKpB,QAAQ,CAAC,CAAC,CAAC,EAAEoB,UAAU,CAACG,QAAQ,KAAK,IAAI,IAAI;IACjD;IACA,CAACe,KAAK,CAACS,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC5B;MACAzB,QAAQ,EAAEmC,IAAI,CAACC,GAAG,CAACrB,KAAK,CAACS,WAAW,CAACI,MAAM,CAACS,EAAE,EAAE,GAAG;IACrD;EACF,CAAC,EAAExC,UAAU,CAACG,QAAQ;EACtB;EACAH,UAAU,CAACG,QAAQ,KAAK,IAAI,IAAI;IAC9B;IACA,CAACe,KAAK,CAACS,WAAW,CAACC,EAAE,CAAC5B,UAAU,CAACG,QAAQ,CAAC,GAAG;MAC3C;MACAA,QAAQ,EAAE,GAAGe,KAAK,CAACS,WAAW,CAACI,MAAM,CAAC/B,UAAU,CAACG,QAAQ,CAAC,GAAGe,KAAK,CAACS,WAAW,CAACU,IAAI;IACrF;EACF,CAAC,CAAC,CAAC;EACH,MAAMI,SAAS,GAAG,aAAa3D,KAAK,CAAC4D,UAAU,CAAC,SAASD,SAASA,CAAClC,OAAO,EAAEoC,GAAG,EAAE;IAC/E,MAAM7C,KAAK,GAAGkB,aAAa,CAACT,OAAO,CAAC;IACpC,MAAM;QACFqC,SAAS;QACTC,SAAS,GAAG,KAAK;QACjBxC,cAAc,GAAG,KAAK;QACtBD,KAAK,GAAG,KAAK;QACbD,QAAQ,GAAG;MACb,CAAC,GAAGL,KAAK;MACTgD,KAAK,GAAGnE,6BAA6B,CAACmB,KAAK,EAAEjB,SAAS,CAAC;IACzD,MAAMmB,UAAU,GAAGpB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;MACrC+C,SAAS;MACTxC,cAAc;MACdD,KAAK;MACLD;IACF,CAAC,CAAC;;IAEF;IACA,MAAMQ,OAAO,GAAGH,iBAAiB,CAACR,UAAU,EAAES,aAAa,CAAC;IAC5D,QACE;MACA;MACAjB,IAAI,CAACyB,aAAa,EAAErC,QAAQ,CAAC;QAC3BmE,EAAE,EAAEF;QACJ;QAAA;;QAEA7C,UAAU,EAAEA,UAAU;QACtB4C,SAAS,EAAE5D,IAAI,CAAC2B,OAAO,CAACV,IAAI,EAAE2C,SAAS,CAAC;QACxCD,GAAG,EAAEA;MACP,CAAC,EAAEG,KAAK,CAAC;IAAC;EAEd,CAAC,CAAC;EACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGT,SAAS,CAACU,SAAS,CAAC,yBAAyB;IACnFC,QAAQ,EAAErE,SAAS,CAACsE,IAAI;IACxB1C,OAAO,EAAE5B,SAAS,CAACuE,MAAM;IACzBV,SAAS,EAAE7D,SAAS,CAACwE,MAAM;IAC3BV,SAAS,EAAE9D,SAAS,CAACyE,WAAW;IAChCnD,cAAc,EAAEtB,SAAS,CAAC0E,IAAI;IAC9BrD,KAAK,EAAErB,SAAS,CAAC0E,IAAI;IACrBtD,QAAQ,EAAEpB,SAAS,CAAC,sCAAsC2E,SAAS,CAAC,CAAC3E,SAAS,CAAC4E,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE5E,SAAS,CAACwE,MAAM,CAAC,CAAC;IAC/IK,EAAE,EAAE7E,SAAS,CAAC2E,SAAS,CAAC,CAAC3E,SAAS,CAAC8E,OAAO,CAAC9E,SAAS,CAAC2E,SAAS,CAAC,CAAC3E,SAAS,CAAC+E,IAAI,EAAE/E,SAAS,CAACuE,MAAM,EAAEvE,SAAS,CAAC0E,IAAI,CAAC,CAAC,CAAC,EAAE1E,SAAS,CAAC+E,IAAI,EAAE/E,SAAS,CAACuE,MAAM,CAAC;EACxJ,CAAC,GAAG,KAAK,CAAC;EACV,OAAOb,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}