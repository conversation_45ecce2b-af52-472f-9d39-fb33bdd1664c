#!/usr/bin/env python3
"""
Redis Setup Script for A.T.L.A.S AI Trading System
Automatically downloads and sets up Redis for Windows
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import shutil
from pathlib import Path

def check_redis_running():
    """Check if Redis is already running"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_connect_timeout=2)
        r.ping()
        print("✅ Redis is already running!")
        return True
    except ImportError:
        print("❌ Redis Python package not installed")
        return False
    except Exception:
        print("⚠️ Redis server not running")
        return False

def install_redis_python_package():
    """Install Redis Python package if not available"""
    try:
        import redis
        print("✅ Redis Python package already installed")
        return True
    except ImportError:
        print("📦 Installing Redis Python package...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "redis"])
            print("✅ Redis Python package installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install Redis Python package: {e}")
            return False

def download_redis_windows():
    """Download Redis for Windows"""
    redis_url = "https://github.com/microsoftarchive/redis/releases/download/win-3.0.504/Redis-x64-3.0.504.zip"
    redis_dir = Path("redis-server")
    
    if redis_dir.exists():
        print("✅ Redis directory already exists")
        return redis_dir
    
    print("📥 Downloading Redis for Windows...")
    try:
        urllib.request.urlretrieve(redis_url, "redis.zip")
        print("✅ Redis downloaded successfully")
        
        print("📂 Extracting Redis...")
        with zipfile.ZipFile("redis.zip", 'r') as zip_ref:
            zip_ref.extractall("redis-server")
        
        os.remove("redis.zip")
        print("✅ Redis extracted successfully")
        return redis_dir
        
    except Exception as e:
        print(f"❌ Failed to download Redis: {e}")
        return None

def start_redis_server(redis_dir):
    """Start Redis server"""
    redis_exe = redis_dir / "redis-server.exe"
    
    if not redis_exe.exists():
        print(f"❌ Redis executable not found at {redis_exe}")
        return False
    
    print("🚀 Starting Redis server...")
    try:
        # Start Redis server in background
        subprocess.Popen([str(redis_exe)], 
                        creationflags=subprocess.CREATE_NEW_CONSOLE)
        print("✅ Redis server started successfully")
        
        # Wait a moment for server to start
        import time
        time.sleep(3)
        
        # Test connection
        if check_redis_running():
            print("✅ Redis server is running and accessible")
            return True
        else:
            print("⚠️ Redis server started but connection test failed")
            return False
            
    except Exception as e:
        print(f"❌ Failed to start Redis server: {e}")
        return False

def create_redis_config():
    """Create basic Redis configuration"""
    config_content = """# Redis configuration for A.T.L.A.S AI Trading System
port 6379
bind 127.0.0.1
timeout 0
save 900 1
save 300 10
save 60 10000
maxmemory 256mb
maxmemory-policy allkeys-lru
"""
    
    with open("redis.conf", "w") as f:
        f.write(config_content)
    print("✅ Redis configuration created")

def setup_redis_service():
    """Setup Redis as Windows service (optional)"""
    print("\n🔧 Redis Service Setup")
    print("To run Redis as a Windows service:")
    print("1. Open Command Prompt as Administrator")
    print("2. Navigate to the redis-server directory")
    print("3. Run: redis-server --service-install redis.conf")
    print("4. Run: redis-server --service-start")

def main():
    """Main setup function"""
    print("🚀 A.T.L.A.S Redis Setup")
    print("=" * 50)
    
    # Check if Redis is already running
    if check_redis_running():
        print("✅ Redis setup complete - server is already running!")
        return
    
    # Install Redis Python package
    if not install_redis_python_package():
        print("❌ Cannot proceed without Redis Python package")
        return
    
    # Try to start Redis if it's already installed
    try:
        subprocess.check_call(["redis-server", "--version"], 
                            stdout=subprocess.DEVNULL, 
                            stderr=subprocess.DEVNULL)
        print("✅ Redis is installed, attempting to start...")
        subprocess.Popen(["redis-server"])
        import time
        time.sleep(3)
        if check_redis_running():
            print("✅ Redis setup complete!")
            return
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ Redis not found in system PATH, downloading...")
    
    # Download and setup Redis
    redis_dir = download_redis_windows()
    if not redis_dir:
        print("❌ Failed to download Redis")
        return
    
    # Create configuration
    create_redis_config()
    
    # Start Redis server
    if start_redis_server(redis_dir):
        print("\n✅ Redis setup complete!")
        print("🎯 A.T.L.A.S can now use Redis for caching and real-time data")
        setup_redis_service()
    else:
        print("❌ Redis setup failed")
        print("📖 Please see REDIS_INSTALLATION_GUIDE.md for manual installation")

if __name__ == "__main__":
    main()
