/* Streamlined A.T.L.A.S Frontend Styles */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  color: #ffffff;
  min-height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Glassmorphism Effects */
.glass {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 12px;
}

.glass-strong {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(6, 182, 212, 0.3);
  border-radius: 12px;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(6, 182, 212, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(6, 182, 212, 0.7);
}

/* Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

.btn-secondary {
  background: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(100, 116, 139, 0.3);
  border-radius: 8px;
  padding: 8px 16px;
  color: #e2e8f0;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: rgba(51, 65, 85, 0.7);
  border-color: rgba(6, 182, 212, 0.5);
}

/* Input Styles */
.input-primary {
  background: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(6, 182, 212, 0.3);
  border-radius: 8px;
  padding: 8px 12px;
  color: white;
  font-size: 14px;
  transition: all 0.2s ease;
}

.input-primary:focus {
  outline: none;
  border-color: #06b6d4;
  box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
}

.input-primary::placeholder {
  color: #94a3b8;
}

/* Card Styles */
.card {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
}

.card:hover {
  border-color: rgba(6, 182, 212, 0.4);
  box-shadow: 0 8px 32px rgba(6, 182, 212, 0.1);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading Spinner */
.spinner {
  border: 2px solid rgba(6, 182, 212, 0.2);
  border-top: 2px solid #06b6d4;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Text Gradient */
.text-gradient {
  background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Status Indicators */
.status-positive {
  color: #10b981;
}

.status-negative {
  color: #ef4444;
}

.status-neutral {
  color: #6b7280;
}

/* Chart Container */
.chart-container {
  background: rgba(15, 23, 42, 0.9);
  border-radius: 8px;
  overflow: hidden;
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid rgba(51, 65, 85, 0.3);
}

.table th {
  background: rgba(30, 41, 59, 0.5);
  font-weight: 600;
  color: #e2e8f0;
}

.table tr:hover {
  background: rgba(51, 65, 85, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    padding: 16px;
  }
  
  .btn-primary,
  .btn-secondary {
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .input-primary {
    padding: 10px 12px;
  }
}

/* Utility Classes */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.opacity-50 { opacity: 0.5; }
.opacity-70 { opacity: 0.7; }
.opacity-80 { opacity: 0.8; }
.opacity-90 { opacity: 0.9; }

/* Color Classes */
.text-cyan-300 { color: #67e8f9; }
.text-cyan-400 { color: #22d3ee; }
.text-blue-400 { color: #60a5fa; }
.text-green-400 { color: #4ade80; }
.text-red-400 { color: #f87171; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }

.bg-cyan-500 { background-color: #06b6d4; }
.bg-slate-700 { background-color: #334155; }
.bg-slate-800 { background-color: #1e293b; }
.bg-slate-900 { background-color: #0f172a; }

/* Border Classes */
.border-cyan-400 { border-color: #22d3ee; }
.border-slate-600 { border-color: #475569; }
.border-slate-700 { border-color: #334155; }
