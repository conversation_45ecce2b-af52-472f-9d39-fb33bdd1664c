import React from 'react';
import { Card, CardContent, Typography, List, ListItem, ListItemText } from '@mui/material';

const PositionsList = ({ positions }) => {
  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Current Positions
        </Typography>
        <List>
          {positions?.length > 0 ? (
            positions.map((position, index) => (
              <ListItem key={index}>
                <ListItemText
                  primary={`${position.symbol} - ${position.qty} shares`}
                  secondary={`Value: $${position.market_value} | P&L: ${position.unrealized_pl}`}
                />
              </ListItem>
            ))
          ) : (
            <Typography variant="body2" color="textSecondary">
              No positions
            </Typography>
          )}
        </List>
      </CardContent>
    </Card>
  );
};

export default PositionsList;
