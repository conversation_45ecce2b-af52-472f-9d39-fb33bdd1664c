import React from 'react';
import { Card, CardContent, Typography, List, ListItem, ListItemText } from '@mui/material';

const SignalsList = ({ signals }) => {
  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Trading Signals
        </Typography>
        <List>
          {signals?.length > 0 ? (
            signals.map((signal, index) => (
              <ListItem key={index}>
                <ListItemText
                  primary={`${signal.symbol} - ${signal.action}`}
                  secondary={`Price: $${signal.price} | Confidence: ${signal.confidence}%`}
                />
              </ListItem>
            ))
          ) : (
            <Typography variant="body2" color="textSecondary">
              No signals available
            </Typography>
          )}
        </List>
      </CardContent>
    </Card>
  );
};

export default SignalsList;
