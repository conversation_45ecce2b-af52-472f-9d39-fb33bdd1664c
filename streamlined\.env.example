# Streamlined A.T.L.A.S Trading System - Environment Variables

# Alpaca Trading API (Paper Trading)
APCA_API_BASE_URL=https://paper-api.alpaca.markets
APCA_API_KEY_ID=PKI0KNC8HXZURYRA4OMC
APCA_API_SECRET_KEY=7ydtObOUVC22xP2IJbEhetmKrvec7N9owdcor0hn

# Financial Modeling Prep API
FMP_API_KEY=K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7
FMP_BASE_URL=https://financialmodelingprep.com/api

# OpenAI API (GPT-4)
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.2

# Web Search APIs (Optional - for enhanced market intelligence)
GOOGLE_SEARCH_API_KEY=AIzaSyAq7wJ7gO60cVtyzcFIp6kIgwPQaHsIny0
GOOGLE_SEARCH_ENGINE_ID=placeholder_need_to_create_custom_search_engine
BING_SEARCH_API_KEY=your_bing_search_api_key

# Application Configuration
ENVIRONMENT=development
DEBUG=True
LOG_LEVEL=INFO
PORT=8080

# Trading Configuration
DEFAULT_RISK_PERCENT=2.0
MAX_POSITIONS=10
PAPER_TRADING=True

# System Performance
API_TIMEOUT=30
CACHE_TTL=300
MAX_SCAN_RESULTS=50

# RAG Education System
ENABLE_RAG=True
VECTOR_DB_PATH=./vector_db
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
