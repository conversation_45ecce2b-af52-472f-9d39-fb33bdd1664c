{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CHatbotfinal\\\\frontend\\\\src\\\\components\\\\SignalsList.js\";\nimport React from 'react';\nimport { Card, CardContent, Typography, List, ListItem, ListItemText } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SignalsList = ({\n  signals\n}) => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Trading Signals\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(List, {\n        children: (signals === null || signals === void 0 ? void 0 : signals.length) > 0 ? signals.map((signal, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n          children: /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: `${signal.symbol} - ${signal.action}`,\n            secondary: `Price: $${signal.price} | Confidence: ${signal.confidence}%`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 15\n        }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: \"No signals available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = SignalsList;\nexport default SignalsList;\nvar _c;\n$RefreshReg$(_c, \"SignalsList\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "List", "ListItem", "ListItemText", "jsxDEV", "_jsxDEV", "SignalsList", "signals", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "signal", "index", "primary", "symbol", "action", "secondary", "price", "confidence", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/src/components/SignalsList.js"], "sourcesContent": ["import React from 'react';\nimport { Card, CardContent, Typography, List, ListItem, ListItemText } from '@mui/material';\n\nconst SignalsList = ({ signals }) => {\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          Trading Signals\n        </Typography>\n        <List>\n          {signals?.length > 0 ? (\n            signals.map((signal, index) => (\n              <ListItem key={index}>\n                <ListItemText\n                  primary={`${signal.symbol} - ${signal.action}`}\n                  secondary={`Price: $${signal.price} | Confidence: ${signal.confidence}%`}\n                />\n              </ListItem>\n            ))\n          ) : (\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              No signals available\n            </Typography>\n          )}\n        </List>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default SignalsList;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5F,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EACnC,oBACEF,OAAA,CAACP,IAAI;IAAAU,QAAA,eACHH,OAAA,CAACN,WAAW;MAAAS,QAAA,gBACVH,OAAA,CAACL,UAAU;QAACS,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbT,OAAA,CAACJ,IAAI;QAAAO,QAAA,EACF,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,MAAM,IAAG,CAAC,GAClBR,OAAO,CAACS,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACxBb,OAAA,CAACH,QAAQ;UAAAM,QAAA,eACPH,OAAA,CAACF,YAAY;YACXgB,OAAO,EAAE,GAAGF,MAAM,CAACG,MAAM,MAAMH,MAAM,CAACI,MAAM,EAAG;YAC/CC,SAAS,EAAE,WAAWL,MAAM,CAACM,KAAK,kBAAkBN,MAAM,CAACO,UAAU;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E;QAAC,GAJWI,KAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKV,CACX,CAAC,gBAEFT,OAAA,CAACL,UAAU;UAACS,OAAO,EAAC,OAAO;UAACgB,KAAK,EAAC,eAAe;UAAAjB,QAAA,EAAC;QAElD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACY,EAAA,GA1BIpB,WAAW;AA4BjB,eAAeA,WAAW;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}