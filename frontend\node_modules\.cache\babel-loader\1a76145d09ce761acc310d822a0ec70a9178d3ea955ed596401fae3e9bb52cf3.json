{"ast": null, "code": "var Observable = /** @class */function () {\n  function Observable(win) {\n    var _this = this;\n    this._resolutionListener = function () {\n      return _this._onResolutionChanged();\n    };\n    this._resolutionMediaQueryList = null;\n    this._observers = [];\n    this._window = win;\n    this._installResolutionListener();\n  }\n  Observable.prototype.dispose = function () {\n    this._uninstallResolutionListener();\n    this._window = null;\n  };\n  Object.defineProperty(Observable.prototype, \"value\", {\n    get: function () {\n      return this._window.devicePixelRatio;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Observable.prototype.subscribe = function (next) {\n    var _this = this;\n    var observer = {\n      next: next\n    };\n    this._observers.push(observer);\n    return {\n      unsubscribe: function () {\n        _this._observers = _this._observers.filter(function (o) {\n          return o !== observer;\n        });\n      }\n    };\n  };\n  Observable.prototype._installResolutionListener = function () {\n    if (this._resolutionMediaQueryList !== null) {\n      throw new Error('Resolution listener is already installed');\n    }\n    var dppx = this._window.devicePixelRatio;\n    this._resolutionMediaQueryList = this._window.matchMedia(\"all and (resolution: \".concat(dppx, \"dppx)\"));\n    // IE and some versions of Edge do not support addEventListener/removeEventListener, and we are going to use the deprecated addListener/removeListener\n    this._resolutionMediaQueryList.addListener(this._resolutionListener);\n  };\n  Observable.prototype._uninstallResolutionListener = function () {\n    if (this._resolutionMediaQueryList !== null) {\n      // IE and some versions of Edge do not support addEventListener/removeEventListener, and we are going to use the deprecated addListener/removeListener\n      this._resolutionMediaQueryList.removeListener(this._resolutionListener);\n      this._resolutionMediaQueryList = null;\n    }\n  };\n  Observable.prototype._reinstallResolutionListener = function () {\n    this._uninstallResolutionListener();\n    this._installResolutionListener();\n  };\n  Observable.prototype._onResolutionChanged = function () {\n    var _this = this;\n    this._observers.forEach(function (observer) {\n      return observer.next(_this._window.devicePixelRatio);\n    });\n    this._reinstallResolutionListener();\n  };\n  return Observable;\n}();\nexport function createObservable(win) {\n  return new Observable(win);\n}", "map": {"version": 3, "names": ["Observable", "win", "_this", "_resolutionListener", "_onResolutionChanged", "_resolutionMediaQueryList", "_observers", "_window", "_installResolutionListener", "prototype", "dispose", "_uninstallResolutionListener", "Object", "defineProperty", "get", "devicePixelRatio", "enumerable", "configurable", "subscribe", "next", "observer", "push", "unsubscribe", "filter", "o", "Error", "dppx", "matchMedia", "concat", "addListener", "removeListener", "_reinstallResolutionListener", "for<PERSON>ach", "createObservable"], "sources": ["C:/Users/<USER>/Desktop/CHatbotfinal/frontend/node_modules/fancy-canvas/device-pixel-ratio.mjs"], "sourcesContent": ["var Observable = /** @class */ (function () {\n    function Observable(win) {\n        var _this = this;\n        this._resolutionListener = function () { return _this._onResolutionChanged(); };\n        this._resolutionMediaQueryList = null;\n        this._observers = [];\n        this._window = win;\n        this._installResolutionListener();\n    }\n    Observable.prototype.dispose = function () {\n        this._uninstallResolutionListener();\n        this._window = null;\n    };\n    Object.defineProperty(Observable.prototype, \"value\", {\n        get: function () {\n            return this._window.devicePixelRatio;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Observable.prototype.subscribe = function (next) {\n        var _this = this;\n        var observer = { next: next };\n        this._observers.push(observer);\n        return {\n            unsubscribe: function () {\n                _this._observers = _this._observers.filter(function (o) { return o !== observer; });\n            },\n        };\n    };\n    Observable.prototype._installResolutionListener = function () {\n        if (this._resolutionMediaQueryList !== null) {\n            throw new Error('Resolution listener is already installed');\n        }\n        var dppx = this._window.devicePixelRatio;\n        this._resolutionMediaQueryList = this._window.matchMedia(\"all and (resolution: \".concat(dppx, \"dppx)\"));\n        // IE and some versions of Edge do not support addEventListener/removeEventListener, and we are going to use the deprecated addListener/removeListener\n        this._resolutionMediaQueryList.addListener(this._resolutionListener);\n    };\n    Observable.prototype._uninstallResolutionListener = function () {\n        if (this._resolutionMediaQueryList !== null) {\n            // IE and some versions of Edge do not support addEventListener/removeEventListener, and we are going to use the deprecated addListener/removeListener\n            this._resolutionMediaQueryList.removeListener(this._resolutionListener);\n            this._resolutionMediaQueryList = null;\n        }\n    };\n    Observable.prototype._reinstallResolutionListener = function () {\n        this._uninstallResolutionListener();\n        this._installResolutionListener();\n    };\n    Observable.prototype._onResolutionChanged = function () {\n        var _this = this;\n        this._observers.forEach(function (observer) { return observer.next(_this._window.devicePixelRatio); });\n        this._reinstallResolutionListener();\n    };\n    return Observable;\n}());\nexport function createObservable(win) {\n    return new Observable(win);\n}\n"], "mappings": "AAAA,IAAIA,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACC,GAAG,EAAE;IACrB,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACC,mBAAmB,GAAG,YAAY;MAAE,OAAOD,KAAK,CAACE,oBAAoB,CAAC,CAAC;IAAE,CAAC;IAC/E,IAAI,CAACC,yBAAyB,GAAG,IAAI;IACrC,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,OAAO,GAAGN,GAAG;IAClB,IAAI,CAACO,0BAA0B,CAAC,CAAC;EACrC;EACAR,UAAU,CAACS,SAAS,CAACC,OAAO,GAAG,YAAY;IACvC,IAAI,CAACC,4BAA4B,CAAC,CAAC;IACnC,IAAI,CAACJ,OAAO,GAAG,IAAI;EACvB,CAAC;EACDK,MAAM,CAACC,cAAc,CAACb,UAAU,CAACS,SAAS,EAAE,OAAO,EAAE;IACjDK,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACP,OAAO,CAACQ,gBAAgB;IACxC,CAAC;IACDC,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFjB,UAAU,CAACS,SAAS,CAACS,SAAS,GAAG,UAAUC,IAAI,EAAE;IAC7C,IAAIjB,KAAK,GAAG,IAAI;IAChB,IAAIkB,QAAQ,GAAG;MAAED,IAAI,EAAEA;IAAK,CAAC;IAC7B,IAAI,CAACb,UAAU,CAACe,IAAI,CAACD,QAAQ,CAAC;IAC9B,OAAO;MACHE,WAAW,EAAE,SAAAA,CAAA,EAAY;QACrBpB,KAAK,CAACI,UAAU,GAAGJ,KAAK,CAACI,UAAU,CAACiB,MAAM,CAAC,UAAUC,CAAC,EAAE;UAAE,OAAOA,CAAC,KAAKJ,QAAQ;QAAE,CAAC,CAAC;MACvF;IACJ,CAAC;EACL,CAAC;EACDpB,UAAU,CAACS,SAAS,CAACD,0BAA0B,GAAG,YAAY;IAC1D,IAAI,IAAI,CAACH,yBAAyB,KAAK,IAAI,EAAE;MACzC,MAAM,IAAIoB,KAAK,CAAC,0CAA0C,CAAC;IAC/D;IACA,IAAIC,IAAI,GAAG,IAAI,CAACnB,OAAO,CAACQ,gBAAgB;IACxC,IAAI,CAACV,yBAAyB,GAAG,IAAI,CAACE,OAAO,CAACoB,UAAU,CAAC,uBAAuB,CAACC,MAAM,CAACF,IAAI,EAAE,OAAO,CAAC,CAAC;IACvG;IACA,IAAI,CAACrB,yBAAyB,CAACwB,WAAW,CAAC,IAAI,CAAC1B,mBAAmB,CAAC;EACxE,CAAC;EACDH,UAAU,CAACS,SAAS,CAACE,4BAA4B,GAAG,YAAY;IAC5D,IAAI,IAAI,CAACN,yBAAyB,KAAK,IAAI,EAAE;MACzC;MACA,IAAI,CAACA,yBAAyB,CAACyB,cAAc,CAAC,IAAI,CAAC3B,mBAAmB,CAAC;MACvE,IAAI,CAACE,yBAAyB,GAAG,IAAI;IACzC;EACJ,CAAC;EACDL,UAAU,CAACS,SAAS,CAACsB,4BAA4B,GAAG,YAAY;IAC5D,IAAI,CAACpB,4BAA4B,CAAC,CAAC;IACnC,IAAI,CAACH,0BAA0B,CAAC,CAAC;EACrC,CAAC;EACDR,UAAU,CAACS,SAAS,CAACL,oBAAoB,GAAG,YAAY;IACpD,IAAIF,KAAK,GAAG,IAAI;IAChB,IAAI,CAACI,UAAU,CAAC0B,OAAO,CAAC,UAAUZ,QAAQ,EAAE;MAAE,OAAOA,QAAQ,CAACD,IAAI,CAACjB,KAAK,CAACK,OAAO,CAACQ,gBAAgB,CAAC;IAAE,CAAC,CAAC;IACtG,IAAI,CAACgB,4BAA4B,CAAC,CAAC;EACvC,CAAC;EACD,OAAO/B,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,OAAO,SAASiC,gBAAgBA,CAAChC,GAAG,EAAE;EAClC,OAAO,IAAID,UAAU,CAACC,GAAG,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}