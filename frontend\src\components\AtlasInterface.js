import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { createChart } from 'lightweight-charts';
import { Send, TrendingUp } from 'lucide-react';
import SpaceBackground from './SpaceBackground';

const AtlasInterface = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'system',
      content: "Certainly! Here is the latest stock quote for AAPL:",
      timestamp: new Date()
    },
    {
      id: 2,
      type: 'stock-quote',
      symbol: 'AAPL',
      price: 149.36,
      change: 1.32,
      changePercent: 1.32,
      company: 'Apple Inc.',
      chartData: null, // Will be generated when chart initializes
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const chartRef = useRef(null);
  const chartInstanceRef = useRef(null);

  // Initialize chart when stock quote message is rendered
  useEffect(() => {
    const stockMessage = messages.find(msg => msg.type === 'stock-quote');
    if (stockMessage && chartRef.current && !chartInstanceRef.current) {
      const chartData = stockMessage.chartData || generateMockChartData();
      initializeChart(chartData);
    }
  }, [messages]);

  const initializeChart = (data) => {
    if (chartInstanceRef.current) {
      chartInstanceRef.current.remove();
    }

    const chart = createChart(chartRef.current, {
      width: chartRef.current.clientWidth || 260,
      height: 100,
      layout: {
        background: { color: 'transparent' },
        textColor: '#67e8f9',
        fontSize: 10,
      },
      grid: {
        vertLines: { color: 'rgba(6, 182, 212, 0.05)' },
        horzLines: { color: 'rgba(6, 182, 212, 0.05)' },
      },
      crosshair: {
        mode: 0,
      },
      rightPriceScale: {
        borderColor: 'rgba(6, 182, 212, 0.2)',
        textColor: '#67e8f9',
        visible: false,
      },
      timeScale: {
        borderColor: 'rgba(6, 182, 212, 0.2)',
        textColor: '#67e8f9',
        timeVisible: false,
        secondsVisible: false,
        visible: false,
      },
      handleScroll: false,
      handleScale: false,
    });

    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#10b981',
      downColor: '#ef4444',
      borderDownColor: '#ef4444',
      borderUpColor: '#10b981',
      wickDownColor: '#ef4444',
      wickUpColor: '#10b981',
    });

    candlestickSeries.setData(data);
    chartInstanceRef.current = chart;

    // Auto-fit content
    chart.timeScale().fitContent();

    // Handle resize
    const resizeObserver = new ResizeObserver(entries => {
      if (entries.length === 0 || entries[0].target !== chartRef.current) return;
      const { width } = entries[0].contentRect;
      chart.applyOptions({ width });
    });

    if (chartRef.current) {
      resizeObserver.observe(chartRef.current);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const newMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    const messageToSend = inputMessage;
    setInputMessage('');
    setIsTyping(true);

    try {
      // Call the real Holly AI backend
      const response = await fetch('/api/v1/holly/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageToSend,
          user_context: {
            timestamp: new Date().toISOString(),
            session_id: 'atlas_session',
            interface: 'atlas'
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setIsTyping(false);

      // Create Holly's response message
      const hollyResponse = {
        id: Date.now() + 1,
        type: 'system',
        content: data.response || "I'm having trouble processing that request right now.",
        timestamp: new Date(),
        response_type: data.type,
        requires_action: data.requires_action,
        trading_plan: data.trading_plan,
        plan_id: data.plan_id,
        function_called: data.function_called
      };

      setMessages(prev => [...prev, hollyResponse]);

      // If Holly provided a stock quote, add it as a separate message
      if (data.type === 'stock_quote' && data.trading_plan) {
        const stockMessage = {
          id: Date.now() + 2,
          type: 'stock-quote',
          symbol: data.trading_plan.symbol || 'AAPL',
          price: data.trading_plan.current_price || 149.36,
          change: data.trading_plan.price_change || 1.32,
          changePercent: data.trading_plan.price_change_percent || 1.32,
          company: data.trading_plan.company_name || 'Apple Inc.',
          chartData: generateMockChartData(),
          timestamp: new Date()
        };
        setMessages(prev => [...prev, stockMessage]);
      } else if (messageToSend.toLowerCase().includes('aapl') || messageToSend.toLowerCase().includes('apple')) {
        // Show AAPL quote for demo purposes to match reference image
        const stockMessage = {
          id: Date.now() + 2,
          type: 'stock-quote',
          symbol: 'AAPL',
          price: 149.36,
          change: 1.32,
          changePercent: 1.32,
          company: 'Apple Inc.',
          chartData: generateMockChartData(),
          timestamp: new Date()
        };
        setMessages(prev => [...prev, stockMessage]);
      }

    } catch (error) {
      console.error('Error calling Holly AI:', error);
      setIsTyping(false);

      const errorMessage = {
        id: Date.now() + 1,
        type: 'system',
        content: "I'm having trouble connecting to my AI brain right now. Please try again in a moment.",
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden">
      <SpaceBackground />

      {/* Main Container - Full Page */}
      <div className="relative z-10 min-h-screen flex flex-col">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="flex-1 flex flex-col h-full"
        >
          {/* Main Chat Interface - Full Height */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="atlas-card-fullscreen flex flex-col h-full"
          >
            {/* Header */}
            <div className="text-center py-6 px-6 border-b border-cyan-500/20">
              <h1 className="text-3xl font-bold text-glow bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-1">
                A.T.L.A.S
              </h1>
              <p className="text-cyan-300/70 text-sm">
                Stock Analysis Chatbot
              </p>
            </div>

            {/* Messages Container - Scrollable */}
            <div className="flex-1 px-6 py-4 overflow-y-auto space-y-4"
          >
              <AnimatePresence>
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3 }}
                  >
                    {message.type === 'system' && (
                      <div className="text-cyan-100/90 text-sm leading-relaxed">
                        {message.content}
                      </div>
                    )}

                    {message.type === 'user' && (
                      <div className="text-right">
                        <div className="inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-2xl text-sm max-w-xs">
                          {message.content}
                        </div>
                      </div>
                    )}

                    {message.type === 'stock-quote' && (
                      <div className="stock-quote-card">
                        {/* Stock Header */}
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <div className="text-xl font-bold text-white mb-1">
                              {message.symbol}
                            </div>
                            <div className="text-cyan-300/70 text-xs">
                              {message.company}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-xl font-bold text-white">
                              {message.price}
                            </div>
                            <div className="flex items-center justify-end text-xs text-green-400">
                              <TrendingUp className="w-3 h-3 mr-1" />
                              +{message.changePercent}%
                            </div>
                          </div>
                        </div>

                        {/* Chart */}
                        <div className="chart-container mb-4">
                          <div ref={chartRef} className="w-full" />
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          <button className="action-btn">
                            Show earnings
                          </button>
                          <button className="action-btn">
                            Analyze trend
                          </button>
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Typing Indicator */}
              <AnimatePresence>
                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="flex items-center space-x-2 text-cyan-400"
                  >
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                    <span className="text-sm">A.T.L.A.S is typing...</span>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Input Area - Fixed at Bottom */}
            <div className="px-6 py-4 border-t border-cyan-500/20 bg-slate-900/50 backdrop-blur-sm">
              <div className="relative">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Send a message..."
                  className="atlas-input"
                />
                <button
                  onClick={handleSendMessage}
                  className="atlas-send-btn"
                >
                  <Send className="w-4 h-4 text-white" />
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

// Generate mock chart data with upward trend like AAPL
function generateMockChartData() {
  const data = [];
  let basePrice = 145;
  const endPrice = 149.36;
  const dataPoints = 30;

  for (let i = 0; i < dataPoints; i++) {
    const time = Math.floor(Date.now() / 1000) - (dataPoints - i) * 1800; // 30-minute intervals
    const progress = i / (dataPoints - 1);

    // Create upward trend with some volatility
    const trendPrice = basePrice + (endPrice - basePrice) * progress;
    const volatility = 0.008; // Reduced volatility for smoother trend
    const randomChange = (Math.random() - 0.5) * volatility * trendPrice;

    const open = i === 0 ? basePrice : data[i - 1].close;
    const close = trendPrice + randomChange;
    const high = Math.max(open, close) + Math.random() * 0.3;
    const low = Math.min(open, close) - Math.random() * 0.2;

    data.push({
      time,
      open: parseFloat(open.toFixed(2)),
      high: parseFloat(high.toFixed(2)),
      low: parseFloat(low.toFixed(2)),
      close: parseFloat(close.toFixed(2)),
    });
  }

  return data;
}

export default AtlasInterface;
