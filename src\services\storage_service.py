"""
Storage service for market data and application state
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import redis.asyncio as redis
from influxdb_client.client.influxdb_client_async import InfluxDBClientAsync
from influxdb_client import Point

from src.core.config import settings
from src.core.logging import get_logger
from src.models.trading import MarketData


class StorageService:
    """Service for storing and retrieving data"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.redis_client: Optional[redis.Redis] = None
        self.influx_client: Optional[InfluxDBClientAsync] = None
        self.write_api = None
        self.query_api = None
        
    async def initialize(self):
        """Initialize storage connections"""
        # Initialize Redis (optional for development)
        try:
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                decode_responses=True
            )

            # Test Redis connection
            await self.redis_client.ping()
            self.logger.info("✅ Redis connection established")

        except Exception as e:
            self.logger.warning(f"⚠️ Redis not available: {e}")
            self.logger.info("📝 Running in development mode without Redis caching")
            self.redis_client = None

        # Initialize InfluxDB (optional for development)
        try:
            self.influx_client = InfluxDBClientAsync(
                url=settings.INFLUXDB_URL,
                token=settings.INFLUXDB_TOKEN,
                org=settings.INFLUXDB_ORG
            )

            self.write_api = self.influx_client.write_api()
            self.query_api = self.influx_client.query_api()

            # Test InfluxDB connection
            await self.query_api.query('buckets()')
            self.logger.info("✅ InfluxDB connection established")

        except Exception as e:
            self.logger.warning(f"⚠️ InfluxDB not available: {e}")
            self.logger.info("📝 Running in development mode without InfluxDB storage")
            self.influx_client = None
            self.write_api = None
            self.query_api = None
            
    async def close(self):
        """Close storage connections"""
        try:
            if self.redis_client:
                await self.redis_client.close()
                
            if self.influx_client:
                await self.influx_client.close()
                
            self.logger.info("Storage connections closed")
            
        except Exception as e:
            self.logger.error(f"Error closing storage: {e}")
            
    async def store_market_data(self, data: List[MarketData]):
        """Store market data in InfluxDB"""
        try:
            points = []
            for item in data:
                point = (
                    Point("market_data")
                    .tag("symbol", item.symbol)
                    .tag("timeframe", item.timeframe.value)
                    .field("open", float(item.open))
                    .field("high", float(item.high))
                    .field("low", float(item.low))
                    .field("close", float(item.close))
                    .field("volume", item.volume)
                    .time(item.timestamp)
                )
                points.append(point)
                
            await self.write_api.write(
                bucket=settings.INFLUXDB_BUCKET,
                record=points
            )
            
        except Exception as e:
            self.logger.error(f"Error storing market data: {e}")
            
    async def get_market_data(
        self, 
        symbol: str, 
        timeframe: str, 
        start: datetime, 
        end: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> List[Dict]:
        """Retrieve market data from InfluxDB"""
        try:
            query = f'''
                from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: {start.isoformat()}Z{f', stop: {end.isoformat()}Z' if end else ''})
                |> filter(fn: (r) => r._measurement == "market_data")
                |> filter(fn: (r) => r.symbol == "{symbol}")
                |> filter(fn: (r) => r.timeframe == "{timeframe}")
                |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                |> sort(columns: ["_time"])
                {f'|> limit(n: {limit})' if limit else ''}
            '''
            
            tables = await self.query_api.query(query)
            
            data = []
            for table in tables:
                for record in table.records:
                    data.append({
                        'symbol': record.values.get('symbol'),
                        'timestamp': record.get_time(),
                        'open': record.values.get('open'),
                        'high': record.values.get('high'),
                        'low': record.values.get('low'),
                        'close': record.values.get('close'),
                        'volume': record.values.get('volume'),
                        'timeframe': record.values.get('timeframe')
                    })
                    
            return data
            
        except Exception as e:
            self.logger.error(f"Error retrieving market data: {e}")
            return []
            
    async def store_quote(self, quote_data: Dict):
        """Store latest quote in Redis"""
        if not self.redis_client:
            self.logger.debug("Redis not available - skipping quote storage")
            return

        try:
            key = f"quote:{quote_data['symbol']}"
            await self.redis_client.setex(
                key,
                300,  # 5 minutes TTL
                json.dumps(quote_data, default=str)
            )

        except Exception as e:
            self.logger.error(f"Error storing quote: {e}")

    async def get_quote(self, symbol: str) -> Optional[Dict]:
        """Get latest quote from Redis"""
        if not self.redis_client:
            self.logger.debug("Redis not available - cannot retrieve cached quote")
            return None

        try:
            key = f"quote:{symbol}"
            data = await self.redis_client.get(key)

            if data:
                return json.loads(data)
            return None

        except Exception as e:
            self.logger.error(f"Error retrieving quote: {e}")
            return None
            
    async def store_signal(self, signal_data: Dict):
        """Store trading signal"""
        try:
            # Store in Redis for quick access
            key = f"signal:{signal_data['id']}"
            await self.redis_client.setex(
                key,
                3600,  # 1 hour TTL
                json.dumps(signal_data, default=str)
            )
            
            # Store in InfluxDB for historical analysis
            point = (
                Point("trading_signals")
                .tag("symbol", signal_data['symbol'])
                .tag("signal_type", signal_data['signal_type'])
                .tag("side", signal_data['side'])
                .field("confidence", signal_data['confidence'])
                .field("entry_price", float(signal_data['entry_price']))
                .field("target_price", float(signal_data.get('target_price', 0)))
                .field("stop_price", float(signal_data.get('stop_price', 0)))
                .time(signal_data['timestamp'])
            )
            
            await self.write_api.write(
                bucket=settings.INFLUXDB_BUCKET,
                record=point
            )
            
        except Exception as e:
            self.logger.error(f"Error storing signal: {e}")
            
    async def get_signals(self, symbol: Optional[str] = None) -> List[Dict]:
        """Get active trading signals"""
        try:
            pattern = f"signal:*{symbol}*" if symbol else "signal:*"
            keys = await self.redis_client.keys(pattern)
            
            signals = []
            for key in keys:
                data = await self.redis_client.get(key)
                if data:
                    signals.append(json.loads(data))
                    
            return signals
            
        except Exception as e:
            self.logger.error(f"Error retrieving signals: {e}")
            return []
            
    async def store_order(self, order_data: Dict):
        """Store order information"""
        try:
            key = f"order:{order_data['id']}"
            await self.redis_client.setex(
                key,
                86400,  # 24 hours TTL
                json.dumps(order_data, default=str)
            )
            
        except Exception as e:
            self.logger.error(f"Error storing order: {e}")
            
    async def get_order(self, order_id: str) -> Optional[Dict]:
        """Get order by ID"""
        try:
            key = f"order:{order_id}"
            data = await self.redis_client.get(key)
            
            if data:
                return json.loads(data)
            return None
            
        except Exception as e:
            self.logger.error(f"Error retrieving order: {e}")
            return None
            
    async def store_cache(self, key: str, value: Any, ttl: int = 300):
        """Store data in cache"""
        if not self.redis_client:
            self.logger.debug("Redis not available - skipping cache storage")
            return

        try:
            await self.redis_client.setex(
                key,
                ttl,
                json.dumps(value, default=str)
            )

        except Exception as e:
            self.logger.error(f"Error storing cache: {e}")

    async def get_cache(self, key: str) -> Optional[Any]:
        """Get data from cache"""
        if not self.redis_client:
            self.logger.debug("Redis not available - cannot retrieve cached data")
            return None

        try:
            data = await self.redis_client.get(key)
            if data:
                return json.loads(data)
            return None

        except Exception as e:
            self.logger.error(f"Error retrieving cache: {e}")
            return None
