"""
Enhanced A.T.L.A.S Trading System - Custom Trading Rules Engine
Personalized trading strategies with validation and rule-based logic
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import numpy as np

from .models import TechnicalIndicators, Quote, OHLCV, TradingSignal, SignalType
from .config import settings


class RuleValidationResult(Enum):
    PASS = "pass"
    FAIL = "fail"
    WARNING = "warning"


class TradingStrategy(Enum):
    TTM_SQUEEZE = "ttm_squeeze"
    EMA_CROSSOVER = "ema_crossover"
    RSI_DIVERGENCE = "rsi_divergence"
    BREAKOUT_MOMENTUM = "breakout_momentum"
    SUPPORT_RESISTANCE = "support_resistance"


class TradingRulesEngine:
    """Custom trading rules engine for personalized strategy validation"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.user_preferences = self._load_default_preferences()
        self.rule_history = []
        
    def _load_default_preferences(self) -> Dict[str, Any]:
        """Load default user trading preferences"""
        return {
            "max_risk_per_trade": 2.0,
            "preferred_timeframes": ["15Min", "1Hour", "1Day"],
            "min_volume_ratio": 1.5,
            "min_rsi_oversold": 30,
            "max_rsi_overbought": 70,
            "preferred_strategies": [
                TradingStrategy.TTM_SQUEEZE,
                TradingStrategy.EMA_CROSSOVER,
                TradingStrategy.BREAKOUT_MOMENTUM
            ],
            "avoid_earnings_days": True,
            "min_market_cap": 1000000000,  # $1B minimum
            "max_position_size_percent": 10.0
        }
    
    def validate_ttm_squeeze_entry(self, signal_data: Dict[str, Any]) -> Tuple[RuleValidationResult, str]:
        """
        Validate TTM Squeeze entry conditions
        
        TTM Squeeze Rules:
        1. Bollinger Bands inside Keltner Channels (squeeze condition)
        2. EMA8 > EMA21 for bullish bias
        3. Momentum direction is up
        4. Volume > 1.5x average
        5. Not in overbought territory (RSI < 70)
        """
        try:
            reasons = []
            
            # Check squeeze condition
            if not signal_data.get('ttm_squeeze', False):
                return RuleValidationResult.FAIL, "TTM Squeeze condition not met - Bollinger Bands not inside Keltner Channels"
            
            # Check EMA alignment for trend
            ema8 = signal_data.get('ema8')
            ema21 = signal_data.get('ema21')
            if not (ema8 and ema21 and ema8 > ema21):
                return RuleValidationResult.FAIL, "EMA alignment invalid - EMA8 must be above EMA21 for bullish bias"
            
            # Check momentum direction
            momentum_direction = signal_data.get('momentum_direction')
            if momentum_direction != 'up':
                return RuleValidationResult.FAIL, f"Momentum direction is {momentum_direction}, need 'up' for entry"
            
            # Check volume confirmation
            volume_ratio = signal_data.get('volume_ratio', 0)
            min_volume = self.user_preferences['min_volume_ratio']
            if volume_ratio < min_volume:
                return RuleValidationResult.FAIL, f"Volume ratio {volume_ratio:.2f} below minimum {min_volume}"
            
            # Check RSI not overbought
            rsi = signal_data.get('rsi')
            if rsi and rsi > self.user_preferences['max_rsi_overbought']:
                return RuleValidationResult.WARNING, f"RSI {rsi:.1f} in overbought territory, proceed with caution"
            
            # All conditions met
            reasons.append(f"TTM Squeeze confirmed with {volume_ratio:.1f}x volume")
            reasons.append(f"EMA8 ({ema8:.2f}) > EMA21 ({ema21:.2f})")
            reasons.append(f"Momentum trending {momentum_direction}")
            
            return RuleValidationResult.PASS, " | ".join(reasons)
            
        except Exception as e:
            self.logger.error(f"Error validating TTM Squeeze: {e}")
            return RuleValidationResult.FAIL, f"Validation error: {e}"
    
    def validate_ema_crossover_entry(self, signal_data: Dict[str, Any]) -> Tuple[RuleValidationResult, str]:
        """
        Validate EMA Crossover entry conditions
        
        EMA Crossover Rules:
        1. EMA8 crosses above EMA21 (bullish crossover)
        2. Price above both EMAs
        3. Volume confirmation (>1.5x average)
        4. RSI between 40-70 (not oversold/overbought)
        5. MACD histogram positive
        """
        try:
            ema8 = signal_data.get('ema8')
            ema21 = signal_data.get('ema21')
            price = signal_data.get('current_price')
            
            # Check crossover
            if not (ema8 and ema21 and ema8 > ema21):
                return RuleValidationResult.FAIL, "No bullish EMA crossover detected"
            
            # Check price above EMAs
            if not (price and price > ema8 and price > ema21):
                return RuleValidationResult.FAIL, "Price must be above both EMAs for valid signal"
            
            # Check volume
            volume_ratio = signal_data.get('volume_ratio', 0)
            if volume_ratio < self.user_preferences['min_volume_ratio']:
                return RuleValidationResult.FAIL, f"Insufficient volume confirmation: {volume_ratio:.2f}x"
            
            # Check RSI range
            rsi = signal_data.get('rsi')
            if rsi and (rsi < 40 or rsi > 70):
                return RuleValidationResult.WARNING, f"RSI {rsi:.1f} outside optimal range (40-70)"
            
            # Check MACD
            macd_histogram = signal_data.get('macd_histogram')
            if macd_histogram and macd_histogram <= 0:
                return RuleValidationResult.WARNING, "MACD histogram not positive, momentum may be weak"
            
            return RuleValidationResult.PASS, f"Valid EMA crossover with {volume_ratio:.1f}x volume, RSI: {rsi:.1f}"
            
        except Exception as e:
            return RuleValidationResult.FAIL, f"Validation error: {e}"
    
    def validate_breakout_momentum_entry(self, signal_data: Dict[str, Any]) -> Tuple[RuleValidationResult, str]:
        """
        Validate Breakout Momentum entry conditions
        
        Breakout Rules:
        1. Price breaks above resistance with volume
        2. RSI > 50 (momentum confirmation)
        3. Volume > 2x average for breakouts
        4. ATR expansion (volatility increase)
        5. No immediate resistance above
        """
        try:
            price = signal_data.get('current_price')
            resistance = signal_data.get('resistance_level')
            volume_ratio = signal_data.get('volume_ratio', 0)
            rsi = signal_data.get('rsi')
            
            # Check breakout
            if not (price and resistance and price > resistance):
                return RuleValidationResult.FAIL, "No valid breakout above resistance detected"
            
            breakout_percent = ((price - resistance) / resistance) * 100
            if breakout_percent < 0.5:
                return RuleValidationResult.FAIL, f"Breakout too weak: {breakout_percent:.2f}% above resistance"
            
            # Check volume (higher requirement for breakouts)
            min_breakout_volume = 2.0
            if volume_ratio < min_breakout_volume:
                return RuleValidationResult.FAIL, f"Insufficient breakout volume: {volume_ratio:.2f}x (need {min_breakout_volume}x)"
            
            # Check momentum
            if rsi and rsi < 50:
                return RuleValidationResult.FAIL, f"RSI {rsi:.1f} below 50, insufficient momentum for breakout"
            
            # Check for immediate resistance
            next_resistance = signal_data.get('next_resistance_level')
            if next_resistance and ((next_resistance - price) / price) < 0.02:  # Less than 2% upside
                return RuleValidationResult.WARNING, "Limited upside - next resistance level very close"
            
            return RuleValidationResult.PASS, f"Valid breakout {breakout_percent:.2f}% above resistance with {volume_ratio:.1f}x volume"
            
        except Exception as e:
            return RuleValidationResult.FAIL, f"Validation error: {e}"
    
    def validate_support_resistance_entry(self, signal_data: Dict[str, Any]) -> Tuple[RuleValidationResult, str]:
        """
        Validate Support/Resistance bounce entry conditions
        """
        try:
            price = signal_data.get('current_price')
            support = signal_data.get('support_level')
            rsi = signal_data.get('rsi')
            
            if not (price and support):
                return RuleValidationResult.FAIL, "Support level not identified"
            
            # Check bounce off support
            distance_from_support = ((price - support) / support) * 100
            if distance_from_support > 2.0:  # More than 2% above support
                return RuleValidationResult.FAIL, f"Price {distance_from_support:.2f}% above support, not a bounce"
            
            # Check oversold condition
            if rsi and rsi > self.user_preferences['min_rsi_oversold']:
                return RuleValidationResult.WARNING, f"RSI {rsi:.1f} not oversold, bounce may be weak"
            
            return RuleValidationResult.PASS, f"Valid support bounce, price {distance_from_support:.2f}% above support"
            
        except Exception as e:
            return RuleValidationResult.FAIL, f"Validation error: {e}"
    
    def validate_trading_signal(self, signal: TradingSignal, market_data: Dict[str, Any]) -> Tuple[RuleValidationResult, str, Dict[str, Any]]:
        """
        Main validation function for any trading signal
        """
        try:
            # Prepare signal data
            signal_data = {
                'symbol': signal.symbol,
                'current_price': signal.entry_price,
                'signal_type': signal.signal_type.value,
                'timeframe': signal.timeframe.value,
                'confidence': signal.confidence,
                **market_data
            }
            
            # Determine strategy and validate
            strategy = self._identify_strategy(signal, market_data)
            
            if strategy == TradingStrategy.TTM_SQUEEZE:
                result, reason = self.validate_ttm_squeeze_entry(signal_data)
            elif strategy == TradingStrategy.EMA_CROSSOVER:
                result, reason = self.validate_ema_crossover_entry(signal_data)
            elif strategy == TradingStrategy.BREAKOUT_MOMENTUM:
                result, reason = self.validate_breakout_momentum_entry(signal_data)
            elif strategy == TradingStrategy.SUPPORT_RESISTANCE:
                result, reason = self.validate_support_resistance_entry(signal_data)
            else:
                result, reason = self._validate_generic_signal(signal_data)
            
            # Add general risk checks
            risk_result, risk_reason = self._validate_risk_parameters(signal, signal_data)
            if risk_result == RuleValidationResult.FAIL:
                result = RuleValidationResult.FAIL
                reason = f"{reason} | RISK CHECK FAILED: {risk_reason}"
            
            # Log validation result
            validation_record = {
                'timestamp': datetime.utcnow(),
                'symbol': signal.symbol,
                'strategy': strategy.value,
                'result': result.value,
                'reason': reason,
                'signal_confidence': signal.confidence
            }
            self.rule_history.append(validation_record)
            
            return result, reason, validation_record
            
        except Exception as e:
            self.logger.error(f"Error validating trading signal: {e}")
            return RuleValidationResult.FAIL, f"Validation system error: {e}", {}
    
    def _identify_strategy(self, signal: TradingSignal, market_data: Dict[str, Any]) -> TradingStrategy:
        """Identify the trading strategy based on signal characteristics"""
        reasoning = signal.reasoning.lower()
        
        if 'squeeze' in reasoning or 'ttm' in reasoning:
            return TradingStrategy.TTM_SQUEEZE
        elif 'crossover' in reasoning or 'ema' in reasoning:
            return TradingStrategy.EMA_CROSSOVER
        elif 'breakout' in reasoning or 'resistance' in reasoning:
            return TradingStrategy.BREAKOUT_MOMENTUM
        elif 'support' in reasoning or 'bounce' in reasoning:
            return TradingStrategy.SUPPORT_RESISTANCE
        else:
            return TradingStrategy.EMA_CROSSOVER  # Default
    
    def _validate_generic_signal(self, signal_data: Dict[str, Any]) -> Tuple[RuleValidationResult, str]:
        """Generic validation for unspecified strategies"""
        volume_ratio = signal_data.get('volume_ratio', 0)
        rsi = signal_data.get('rsi')
        
        if volume_ratio < 1.2:
            return RuleValidationResult.FAIL, "Insufficient volume for any trade entry"
        
        if rsi and (rsi < 20 or rsi > 80):
            return RuleValidationResult.WARNING, f"Extreme RSI {rsi:.1f} - high risk trade"
        
        return RuleValidationResult.PASS, "Basic validation criteria met"
    
    def _validate_risk_parameters(self, signal: TradingSignal, signal_data: Dict[str, Any]) -> Tuple[RuleValidationResult, str]:
        """Validate risk management parameters"""
        try:
            # Check stop loss exists
            if not signal.stop_loss:
                return RuleValidationResult.FAIL, "No stop loss defined"
            
            # Check risk percentage
            risk_percent = abs(signal.entry_price - signal.stop_loss) / signal.entry_price * 100
            max_risk = self.user_preferences['max_risk_per_trade']
            
            if risk_percent > max_risk:
                return RuleValidationResult.FAIL, f"Risk {risk_percent:.2f}% exceeds maximum {max_risk}%"
            
            # Check reward-to-risk ratio
            if signal.target_price:
                reward = abs(signal.target_price - signal.entry_price)
                risk = abs(signal.entry_price - signal.stop_loss)
                rr_ratio = reward / risk if risk > 0 else 0
                
                if rr_ratio < 1.5:
                    return RuleValidationResult.WARNING, f"Low reward-to-risk ratio: {rr_ratio:.2f}"
            
            return RuleValidationResult.PASS, f"Risk {risk_percent:.2f}% within acceptable limits"
            
        except Exception as e:
            return RuleValidationResult.FAIL, f"Risk validation error: {e}"
    
    def get_user_preferences(self) -> Dict[str, Any]:
        """Get current user trading preferences"""
        return self.user_preferences.copy()
    
    def update_user_preferences(self, preferences: Dict[str, Any]) -> bool:
        """Update user trading preferences"""
        try:
            self.user_preferences.update(preferences)
            self.logger.info(f"Updated user preferences: {preferences}")
            return True
        except Exception as e:
            self.logger.error(f"Error updating preferences: {e}")
            return False
    
    def get_validation_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent validation history"""
        return self.rule_history[-limit:] if self.rule_history else []
    
    def get_strategy_performance(self) -> Dict[str, Any]:
        """Get performance statistics for each strategy"""
        if not self.rule_history:
            return {}
        
        strategy_stats = {}
        for record in self.rule_history:
            strategy = record['strategy']
            if strategy not in strategy_stats:
                strategy_stats[strategy] = {
                    'total_signals': 0,
                    'passed_signals': 0,
                    'failed_signals': 0,
                    'warning_signals': 0,
                    'avg_confidence': 0
                }
            
            stats = strategy_stats[strategy]
            stats['total_signals'] += 1
            stats['avg_confidence'] += record['signal_confidence']
            
            if record['result'] == 'pass':
                stats['passed_signals'] += 1
            elif record['result'] == 'fail':
                stats['failed_signals'] += 1
            else:
                stats['warning_signals'] += 1
        
        # Calculate averages and percentages
        for strategy, stats in strategy_stats.items():
            if stats['total_signals'] > 0:
                stats['avg_confidence'] /= stats['total_signals']
                stats['pass_rate'] = stats['passed_signals'] / stats['total_signals'] * 100
        
        return strategy_stats
