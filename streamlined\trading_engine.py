"""
Streamlined A.T.L.A.S Trading System - Trading Engine
Consolidated trading execution, portfolio management, and order routing
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal
import uuid

try:
    import alpaca_trade_api as tradeapi
    ALPACA_AVAILABLE = True
except ImportError:
    ALPACA_AVAILABLE = False
    logging.warning("Alpaca Trade API not available. Install with: pip install alpaca-trade-api")

from .config import settings, get_api_headers, get_api_url
from .models import (
    Order, Position, OrderSide, OrderType, OrderStatus, TradingSignal,
    PortfolioMetrics, OrderRequest
)
from .market_data import MarketDataService
from .ai_services import AIServices


class TradingEngine:
    """Consolidated trading engine with execution, portfolio management, and AI integration"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.alpaca_client = None
        self.market_data = MarketDataService()
        self.ai_services = AIServices()
        
        # Trading state
        self.active_orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        self.portfolio_metrics = PortfolioMetrics(
            total_value=0, cash=0, buying_power=0, day_pl=0, day_pl_percent=0,
            total_pl=0, total_pl_percent=0, positions_count=0
        )
        
        # Initialize Alpaca client
        self._initialize_alpaca()
    
    def _initialize_alpaca(self):
        """Initialize Alpaca trading client"""
        if not ALPACA_AVAILABLE:
            self.logger.warning("Alpaca not available - trading functions disabled")
            return
        
        try:
            self.alpaca_client = tradeapi.REST(
                key_id=settings.APCA_API_KEY_ID,
                secret_key=settings.APCA_API_SECRET_KEY,
                base_url=settings.APCA_API_BASE_URL,
                api_version='v2'
            )
            
            # Test connection
            account = self.alpaca_client.get_account()
            self.logger.info(f"Connected to Alpaca account: {account.status}")
            
            if settings.PAPER_TRADING:
                self.logger.info("Running in PAPER TRADING mode")
            else:
                self.logger.warning("Running in LIVE TRADING mode")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Alpaca client: {e}")
            self.alpaca_client = None
    
    async def place_order(self, order_request: OrderRequest) -> Optional[str]:
        """Place a trading order"""
        if not self.alpaca_client:
            self.logger.error("Alpaca client not available")
            return None
        
        try:
            # Create order object
            order = Order(
                id=str(uuid.uuid4()),
                symbol=order_request.symbol.upper(),
                qty=order_request.qty,
                side=order_request.side,
                order_type=order_request.order_type,
                limit_price=order_request.limit_price,
                stop_price=order_request.stop_price,
                status=OrderStatus.NEW,
                created_at=datetime.utcnow()
            )
            
            # Build Alpaca order parameters
            order_params = {
                "symbol": order.symbol,
                "qty": order.qty,
                "side": order.side.value,
                "type": order.order_type.value,
                "time_in_force": "day"
            }
            
            # Add price parameters based on order type
            if order.order_type == OrderType.LIMIT and order.limit_price:
                order_params["limit_price"] = order.limit_price
            elif order.order_type == OrderType.STOP and order.stop_price:
                order_params["stop_price"] = order.stop_price
            elif order.order_type == OrderType.STOP_LIMIT:
                if order.limit_price and order.stop_price:
                    order_params["limit_price"] = order.limit_price
                    order_params["stop_price"] = order.stop_price
            
            # Submit order to Alpaca
            alpaca_order = self.alpaca_client.submit_order(**order_params)
            
            # Update order with Alpaca response
            order.id = alpaca_order.id
            order.status = OrderStatus(alpaca_order.status)
            order.created_at = alpaca_order.created_at
            order.updated_at = alpaca_order.updated_at
            
            # Store order
            self.active_orders[order.id] = order
            
            self.logger.info(f"Order placed: {order.id} - {order.side.value} {order.qty} {order.symbol}")
            return order.id
            
        except Exception as e:
            self.logger.error(f"Error placing order: {e}")
            return None
    
    async def place_bracket_order(self, symbol: str, qty: float, entry_price: float, 
                                target_price: float, stop_price: float) -> Optional[Dict[str, str]]:
        """Place a bracket order with entry, target, and stop"""
        if not self.alpaca_client:
            return None
        
        try:
            # Determine order side based on current position or default to buy
            side = OrderSide.BUY  # Simplified for now
            
            # Submit bracket order to Alpaca
            bracket_order = self.alpaca_client.submit_order(
                symbol=symbol.upper(),
                qty=qty,
                side=side.value,
                type="limit",
                time_in_force="day",
                limit_price=entry_price,
                order_class="bracket",
                take_profit={"limit_price": target_price},
                stop_loss={"stop_price": stop_price}
            )
            
            # Extract order IDs
            main_order_id = bracket_order.id
            take_profit_id = bracket_order.legs[0].id if bracket_order.legs else None
            stop_loss_id = bracket_order.legs[1].id if len(bracket_order.legs) > 1 else None
            
            self.logger.info(f"Bracket order placed for {symbol}: Entry={main_order_id}, TP={take_profit_id}, SL={stop_loss_id}")
            
            return {
                "main_order": main_order_id,
                "take_profit": take_profit_id,
                "stop_loss": stop_loss_id
            }
            
        except Exception as e:
            self.logger.error(f"Error placing bracket order: {e}")
            return None
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an active order"""
        if not self.alpaca_client:
            return False
        
        try:
            self.alpaca_client.cancel_order(order_id)
            
            # Update local order status
            if order_id in self.active_orders:
                self.active_orders[order_id].status = OrderStatus.CANCELED
                self.active_orders[order_id].updated_at = datetime.utcnow()
            
            self.logger.info(f"Order canceled: {order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error canceling order {order_id}: {e}")
            return False
    
    async def get_positions(self) -> List[Position]:
        """Get current positions"""
        if not self.alpaca_client:
            return []
        
        try:
            alpaca_positions = self.alpaca_client.list_positions()
            positions = []
            
            for pos in alpaca_positions:
                position = Position(
                    symbol=pos.symbol,
                    qty=float(pos.qty),
                    side=pos.side,
                    market_value=float(pos.market_value),
                    cost_basis=float(pos.cost_basis),
                    unrealized_pl=float(pos.unrealized_pl),
                    unrealized_plpc=float(pos.unrealized_plpc),
                    current_price=float(pos.current_price),
                    lastday_price=float(pos.lastday_price),
                    change_today=float(pos.change_today)
                )
                positions.append(position)
                self.positions[pos.symbol] = position
            
            return positions
            
        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []
    
    async def get_portfolio_metrics(self) -> PortfolioMetrics:
        """Get current portfolio metrics"""
        if not self.alpaca_client:
            return self.portfolio_metrics
        
        try:
            account = self.alpaca_client.get_account()
            positions = await self.get_positions()
            
            self.portfolio_metrics = PortfolioMetrics(
                total_value=float(account.portfolio_value),
                cash=float(account.cash),
                buying_power=float(account.buying_power),
                day_pl=float(account.daytrading_buying_power) if hasattr(account, 'daytrading_buying_power') else 0,
                day_pl_percent=0,  # Calculate separately
                total_pl=float(account.portfolio_value) - 100000,  # Assuming $100k starting capital
                total_pl_percent=((float(account.portfolio_value) - 100000) / 100000) * 100,
                positions_count=len(positions),
                last_updated=datetime.utcnow()
            )
            
            return self.portfolio_metrics
            
        except Exception as e:
            self.logger.error(f"Error getting portfolio metrics: {e}")
            return self.portfolio_metrics
    
    async def calculate_position_size(self, symbol: str, entry_price: float, stop_price: float, 
                                    risk_percent: float = 2.0) -> float:
        """Calculate position size based on risk management"""
        try:
            # Get account value
            portfolio = await self.get_portfolio_metrics()
            account_value = portfolio.total_value
            
            if account_value <= 0:
                account_value = 100000  # Default for paper trading
            
            # Calculate risk amount
            risk_amount = account_value * (risk_percent / 100)
            
            # Calculate risk per share
            risk_per_share = abs(entry_price - stop_price)
            
            if risk_per_share <= 0:
                return 0
            
            # Calculate position size
            position_size = risk_amount / risk_per_share
            
            # Round down to avoid over-risking
            position_size = int(position_size)
            
            self.logger.info(f"Position size for {symbol}: {position_size} shares (Risk: ${risk_amount:.2f})")
            return position_size
            
        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return 0
    
    async def create_ai_trading_plan(self, goal: str, symbols: Optional[List[str]] = None) -> Dict[str, Any]:
        """Create AI-generated trading plan"""
        try:
            # Use AI services to analyze market and create plan
            plan_response = await self.ai_services.process_chat_message(
                f"Create a trading plan to {goal}",
                context={"symbols": symbols or []}
            )
            
            # For now, return a simplified plan structure
            plan = {
                "id": str(uuid.uuid4()),
                "goal": goal,
                "created_at": datetime.utcnow().isoformat(),
                "ai_analysis": plan_response.response,
                "recommended_symbols": symbols or [],
                "risk_management": {
                    "max_risk_per_trade": settings.DEFAULT_RISK_PERCENT,
                    "max_positions": settings.MAX_POSITIONS
                },
                "status": "draft"
            }
            
            return plan
            
        except Exception as e:
            self.logger.error(f"Error creating AI trading plan: {e}")
            return {}
    
    async def execute_signal(self, signal: TradingSignal) -> Optional[str]:
        """Execute a trading signal"""
        try:
            # Calculate position size
            if signal.stop_loss:
                position_size = await self.calculate_position_size(
                    signal.symbol, 
                    signal.entry_price, 
                    signal.stop_loss
                )
            else:
                # Use AI to calculate stop loss
                stop_result = await self.ai_services.calculate_ai_stop_loss(
                    signal.symbol,
                    signal.entry_price,
                    "long" if signal.signal_type.value == "buy" else "short"
                )
                signal.stop_loss = stop_result.get("stop_price", signal.entry_price * 0.98)
                
                position_size = await self.calculate_position_size(
                    signal.symbol,
                    signal.entry_price, 
                    signal.stop_loss
                )
            
            if position_size <= 0:
                self.logger.warning(f"Invalid position size for {signal.symbol}")
                return None
            
            # Create order request
            order_request = OrderRequest(
                symbol=signal.symbol,
                qty=position_size,
                side=OrderSide.BUY if signal.signal_type.value == "buy" else OrderSide.SELL,
                order_type=OrderType.LIMIT,
                limit_price=signal.entry_price
            )
            
            # Place order
            order_id = await self.place_order(order_request)
            
            if order_id and signal.target_price and signal.stop_loss:
                # Place bracket order for better risk management
                bracket_result = await self.place_bracket_order(
                    signal.symbol,
                    position_size,
                    signal.entry_price,
                    signal.target_price,
                    signal.stop_loss
                )
                
                if bracket_result:
                    self.logger.info(f"Bracket order placed for signal: {signal.symbol}")
                    return bracket_result["main_order"]
            
            return order_id
            
        except Exception as e:
            self.logger.error(f"Error executing signal for {signal.symbol}: {e}")
            return None
    
    async def get_account_summary(self) -> Dict[str, Any]:
        """Get comprehensive account summary"""
        try:
            portfolio = await self.get_portfolio_metrics()
            positions = await self.get_positions()
            
            # Get active orders
            active_orders = []
            if self.alpaca_client:
                try:
                    alpaca_orders = self.alpaca_client.list_orders(status='open')
                    active_orders = [
                        {
                            "id": order.id,
                            "symbol": order.symbol,
                            "side": order.side,
                            "qty": order.qty,
                            "type": order.order_type,
                            "status": order.status
                        }
                        for order in alpaca_orders
                    ]
                except Exception as e:
                    self.logger.warning(f"Error getting active orders: {e}")
            
            return {
                "portfolio": portfolio.dict(),
                "positions": [pos.dict() for pos in positions],
                "active_orders": active_orders,
                "trading_enabled": self.alpaca_client is not None,
                "paper_trading": settings.PAPER_TRADING,
                "last_updated": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting account summary: {e}")
            return {}
    
    async def start_portfolio_monitoring(self, update_interval: int = 30):
        """Start background portfolio monitoring"""
        while True:
            try:
                await self.get_portfolio_metrics()
                await self.get_positions()
                await asyncio.sleep(update_interval)
            except Exception as e:
                self.logger.error(f"Error in portfolio monitoring: {e}")
                await asyncio.sleep(60)  # Wait longer on error
