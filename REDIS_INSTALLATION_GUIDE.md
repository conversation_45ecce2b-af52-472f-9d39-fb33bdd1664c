# Redis Installation Guide for A.T.L.A.S AI Trading System

Redis is required for the A.T.L.A.S AI Trading System to handle caching, real-time data storage, and session management.

## Option 1: Install Redis using Docker (Recommended - Easiest)

### Prerequisites
- Docker Desktop for Windows installed

### Installation Steps
1. **Pull and run Redis container:**
   ```bash
   docker run -d --name atlas-redis -p 6379:6379 redis:latest
   ```

2. **Verify <PERSON><PERSON> is running:**
   ```bash
   docker ps
   ```

3. **Test Redis connection:**
   ```bash
   docker exec -it atlas-redis redis-cli ping
   ```
   Should return: `PONG`

4. **To stop Redis:**
   ```bash
   docker stop atlas-redis
   ```

5. **To start Redis again:**
   ```bash
   docker start atlas-redis
   ```

## Option 2: Install Redis using WSL2 (Official Method)

### Prerequisites
- Windows 10 version 2004 or higher, or Windows 11
- WSL2 enabled

### Installation Steps
1. **Install WSL2 (if not already installed):**
   ```powershell
   wsl --install
   ```

2. **Open WSL2 terminal and update packages:**
   ```bash
   sudo apt update
   sudo apt upgrade
   ```

3. **Install Redis:**
   ```bash
   sudo apt install redis-server
   ```

4. **Start Redis service:**
   ```bash
   sudo service redis-server start
   ```

5. **Test Redis:**
   ```bash
   redis-cli ping
   ```

## Option 3: Install Redis using Chocolatey

### Prerequisites
- Chocolatey package manager installed

### Installation Steps
1. **Install Chocolatey (if not installed):**
   Open PowerShell as Administrator and run:
   ```powershell
   Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
   ```

2. **Install Redis:**
   ```powershell
   choco install redis-64 -y
   ```

3. **Start Redis service:**
   ```powershell
   redis-server
   ```

## Option 4: Manual Installation (Windows Binary)

### Download and Setup
1. **Download Redis for Windows:**
   - Visit: https://github.com/microsoftarchive/redis/releases
   - Download the latest `.msi` file (e.g., `Redis-x64-3.0.504.msi`)

2. **Install Redis:**
   - Run the downloaded `.msi` file
   - Follow the installation wizard
   - Choose to install as a Windows service

3. **Start Redis service:**
   - Open Services (services.msc)
   - Find "Redis" service
   - Right-click and select "Start"

## Verification and Testing

### Test Redis Connection
```bash
# Test basic connection
redis-cli ping

# Test set/get operations
redis-cli set test "Hello A.T.L.A.S"
redis-cli get test
```

### Configure A.T.L.A.S to Use Redis
Your `.env` file already has Redis configuration:
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

### Test A.T.L.A.S Redis Integration
```python
# Test script to verify Redis connection
python -c "
import redis
try:
    r = redis.Redis(host='localhost', port=6379, db=0)
    r.ping()
    print('✅ Redis connection successful!')
    r.set('atlas_test', 'Redis working with A.T.L.A.S')
    print(f'✅ Test value: {r.get(\"atlas_test\").decode()}')
except Exception as e:
    print(f'❌ Redis connection failed: {e}')
"
```

## Troubleshooting

### Common Issues
1. **Port 6379 already in use:**
   - Check if another Redis instance is running
   - Use `netstat -an | findstr 6379` to check port usage

2. **Redis service won't start:**
   - Check Windows Event Viewer for error details
   - Ensure no firewall blocking

3. **Connection refused:**
   - Verify Redis is running: `redis-cli ping`
   - Check Redis configuration file

### Redis Configuration for A.T.L.A.S
Create `redis.conf` if needed:
```conf
# Basic Redis configuration for A.T.L.A.S
port 6379
bind 127.0.0.1
timeout 0
save 900 1
save 300 10
save 60 10000
```

## Next Steps
After Redis is installed and running:
1. Test the A.T.L.A.S system: `python atlas_ai_server.py`
2. Verify Redis integration in the A.T.L.A.S logs
3. Test real-time data caching functionality

## Redis Management Commands
```bash
# Start Redis server
redis-server

# Connect to Redis CLI
redis-cli

# Check Redis info
redis-cli info

# Monitor Redis commands
redis-cli monitor

# Flush all data (use with caution)
redis-cli flushall
```

Redis is now ready to support the A.T.L.A.S AI Trading System's caching and real-time data requirements!
