# Streamlined A.T.L.A.S Trading System Requirements

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP Client
aiohttp==3.9.1
requests==2.31.0

# Trading APIs
alpaca-trade-api==3.1.1

# AI and LLM
openai==1.3.7

# Vector Database (RAG)
chromadb==0.4.18

# Data Processing
numpy==1.24.3
pandas==2.0.3

# Environment Management
python-dotenv==1.0.0

# Logging and Utilities
python-json-logger==2.0.7

# Optional: Enhanced Features
# langchain==0.0.350  # For advanced RAG features
# pinecone-client==2.2.4  # Alternative vector database
# yfinance==0.2.18  # Alternative market data source
