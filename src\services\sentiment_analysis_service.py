"""
Comprehensive News & Sentiment Analysis Service for A.T.L.A.S AI Trading System
Integrates web search, FMP news, and AI-powered sentiment analysis
"""

import asyncio
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from decimal import Decimal

from src.core.config import settings
from src.core.logging import get_logger
from src.services.web_search_service import WebSearchService
from src.services.fmp_service import FMPService
from src.services.llm_service import LLMService


@dataclass
class SentimentResult:
    """Sentiment analysis result"""
    symbol: str
    sentiment: str  # "bullish", "bearish", "neutral"
    score: float  # -1.0 to +1.0
    magnitude: str  # "low", "medium", "high"
    confidence: float  # 0.0 to 1.0
    reasoning: str
    news_count: int
    timestamp: datetime


@dataclass
class NewsArticle:
    """News article data structure"""
    title: str
    content: str
    url: str
    published_date: datetime
    source: str
    symbol: Optional[str] = None
    sentiment_score: Optional[float] = None
    impact_score: Optional[float] = None


class SentimentAnalysisService:
    """Comprehensive sentiment analysis service"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.web_search = WebSearchService()
        self.fmp_service = FMPService()
        self.llm_service = LLMService()
        
        # Sentiment keywords for quick analysis
        self.bullish_keywords = [
            'beat', 'exceed', 'growth', 'profit', 'gain', 'up', 'bullish', 'positive', 
            'strong', 'rally', 'surge', 'breakthrough', 'upgrade', 'outperform',
            'revenue growth', 'earnings beat', 'expansion', 'acquisition', 'partnership'
        ]
        
        self.bearish_keywords = [
            'miss', 'decline', 'loss', 'down', 'bearish', 'negative', 'weak', 'fall',
            'crash', 'plunge', 'downgrade', 'underperform', 'recession', 'bankruptcy',
            'lawsuit', 'investigation', 'scandal', 'warning', 'guidance cut'
        ]
        
        # Cache for sentiment results
        self.sentiment_cache: Dict[str, SentimentResult] = {}
        self.cache_ttl = 300  # 5 minutes
        
    async def analyze_symbol_sentiment(
        self, 
        symbol: str, 
        time_range: str = "1d",
        include_social: bool = True,
        include_news: bool = True
    ) -> SentimentResult:
        """Comprehensive sentiment analysis for a symbol"""
        try:
            # Check cache first
            cache_key = f"{symbol}_{time_range}_{include_social}_{include_news}"
            if cache_key in self.sentiment_cache:
                cached_result = self.sentiment_cache[cache_key]
                if (datetime.now() - cached_result.timestamp).seconds < self.cache_ttl:
                    return cached_result
            
            self.logger.info(f"Analyzing sentiment for {symbol} over {time_range}")
            
            # Gather news from multiple sources
            news_articles = []
            
            if include_news:
                # Get FMP news
                fmp_news = await self._get_fmp_news(symbol, time_range)
                news_articles.extend(fmp_news)
                
                # Get web search news
                web_news = await self._get_web_news(symbol, time_range)
                news_articles.extend(web_news)
            
            if include_social:
                # Get social sentiment (placeholder for now)
                social_sentiment = await self._get_social_sentiment(symbol)
                # This would integrate with Twitter API, Reddit API, etc.
            
            # Analyze sentiment using multiple methods
            sentiment_scores = []
            
            # Method 1: Keyword-based analysis
            keyword_sentiment = self._analyze_keyword_sentiment(news_articles)
            sentiment_scores.append(keyword_sentiment)
            
            # Method 2: AI-powered analysis
            if news_articles:
                ai_sentiment = await self._analyze_ai_sentiment(symbol, news_articles[:10])
                sentiment_scores.append(ai_sentiment)
            
            # Method 3: Market impact analysis
            market_sentiment = await self._analyze_market_impact(symbol, news_articles)
            sentiment_scores.append(market_sentiment)
            
            # Combine sentiment scores
            final_sentiment = self._combine_sentiment_scores(sentiment_scores)
            
            # Create result
            result = SentimentResult(
                symbol=symbol,
                sentiment=final_sentiment["sentiment"],
                score=final_sentiment["score"],
                magnitude=final_sentiment["magnitude"],
                confidence=final_sentiment["confidence"],
                reasoning=final_sentiment["reasoning"],
                news_count=len(news_articles),
                timestamp=datetime.now()
            )
            
            # Cache result
            self.sentiment_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing sentiment for {symbol}: {e}")
            return SentimentResult(
                symbol=symbol,
                sentiment="neutral",
                score=0.0,
                magnitude="low",
                confidence=0.0,
                reasoning=f"Analysis failed: {str(e)}",
                news_count=0,
                timestamp=datetime.now()
            )
    
    async def analyze_market_sentiment(
        self, 
        symbols: List[str] = None,
        time_range: str = "1d"
    ) -> Dict[str, Any]:
        """Analyze overall market sentiment"""
        try:
            if symbols is None:
                symbols = ["SPY", "QQQ", "IWM", "DIA"]  # Major market ETFs
            
            # Analyze sentiment for each symbol
            symbol_sentiments = {}
            for symbol in symbols:
                sentiment = await self.analyze_symbol_sentiment(symbol, time_range)
                symbol_sentiments[symbol] = sentiment
            
            # Calculate overall market sentiment
            total_score = sum(s.score for s in symbol_sentiments.values())
            avg_score = total_score / len(symbol_sentiments)
            
            # Determine overall sentiment
            if avg_score > 0.2:
                overall_sentiment = "bullish"
            elif avg_score < -0.2:
                overall_sentiment = "bearish"
            else:
                overall_sentiment = "neutral"
            
            # Calculate confidence
            confidence_scores = [s.confidence for s in symbol_sentiments.values()]
            avg_confidence = sum(confidence_scores) / len(confidence_scores)
            
            return {
                "overall_sentiment": overall_sentiment,
                "overall_score": avg_score,
                "confidence": avg_confidence,
                "symbol_breakdown": {
                    symbol: {
                        "sentiment": s.sentiment,
                        "score": s.score,
                        "confidence": s.confidence,
                        "news_count": s.news_count
                    } for symbol, s in symbol_sentiments.items()
                },
                "analysis_timestamp": datetime.now().isoformat(),
                "time_range": time_range
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing market sentiment: {e}")
            return {"error": str(e)}
    
    async def get_sentiment_driven_opportunities(
        self, 
        min_sentiment_score: float = 0.3,
        min_confidence: float = 0.6
    ) -> List[Dict[str, Any]]:
        """Find trading opportunities based on sentiment analysis"""
        try:
            # Get list of active symbols (this would come from universe manager)
            symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "NFLX"]
            
            opportunities = []
            
            for symbol in symbols:
                sentiment = await self.analyze_symbol_sentiment(symbol)
                
                # Check if sentiment meets criteria
                if (abs(sentiment.score) >= min_sentiment_score and 
                    sentiment.confidence >= min_confidence):
                    
                    opportunity = {
                        "symbol": symbol,
                        "sentiment": sentiment.sentiment,
                        "score": sentiment.score,
                        "confidence": sentiment.confidence,
                        "magnitude": sentiment.magnitude,
                        "reasoning": sentiment.reasoning,
                        "news_count": sentiment.news_count,
                        "recommendation": self._get_trading_recommendation(sentiment)
                    }
                    opportunities.append(opportunity)
            
            # Sort by sentiment strength
            opportunities.sort(key=lambda x: abs(x["score"]), reverse=True)
            
            return opportunities
            
        except Exception as e:
            self.logger.error(f"Error finding sentiment opportunities: {e}")
            return []
    
    async def _get_fmp_news(self, symbol: str, time_range: str) -> List[NewsArticle]:
        """Get news from FMP API"""
        try:
            # Calculate date range
            end_date = datetime.now()
            if time_range == "1h":
                start_date = end_date - timedelta(hours=1)
            elif time_range == "4h":
                start_date = end_date - timedelta(hours=4)
            elif time_range == "1d":
                start_date = end_date - timedelta(days=1)
            elif time_range == "3d":
                start_date = end_date - timedelta(days=3)
            else:
                start_date = end_date - timedelta(days=1)
            
            async with self.fmp_service as fmp:
                news_data = await fmp.get_stock_news(
                    symbol=symbol, 
                    limit=20, 
                    from_date=start_date, 
                    to_date=end_date
                )
            
            articles = []
            for article in news_data:
                try:
                    published_date = datetime.fromisoformat(
                        article.get("publishedDate", "").replace("Z", "+00:00")
                    )
                except:
                    published_date = datetime.now()
                
                articles.append(NewsArticle(
                    title=article.get("title", ""),
                    content=article.get("text", ""),
                    url=article.get("url", ""),
                    published_date=published_date,
                    source="FMP",
                    symbol=symbol
                ))
            
            return articles
            
        except Exception as e:
            self.logger.error(f"Error getting FMP news for {symbol}: {e}")
            return []
    
    async def _get_web_news(self, symbol: str, time_range: str) -> List[NewsArticle]:
        """Get news from web search"""
        try:
            # Create search query
            query = f"{symbol} stock news earnings financial results"
            
            search_results = await self.web_search.search_financial_news(symbol, num_results=10)
            
            if not search_results.get("success"):
                return []
            
            articles = []
            for result in search_results.get("results", []):
                articles.append(NewsArticle(
                    title=result.get("title", ""),
                    content=result.get("snippet", ""),
                    url=result.get("url", ""),
                    published_date=datetime.now(),  # Web search doesn't always provide dates
                    source="Web Search",
                    symbol=symbol
                ))
            
            return articles
            
        except Exception as e:
            self.logger.error(f"Error getting web news for {symbol}: {e}")
            return []

    async def _get_social_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Get social media sentiment (placeholder for future implementation)"""
        # This would integrate with Twitter API, Reddit API, StockTwits, etc.
        return {
            "sentiment": "neutral",
            "score": 0.0,
            "confidence": 0.0,
            "source": "social_media"
        }

    def _analyze_keyword_sentiment(self, articles: List[NewsArticle]) -> Dict[str, Any]:
        """Analyze sentiment using keyword matching"""
        try:
            if not articles:
                return {"sentiment": "neutral", "score": 0.0, "confidence": 0.0}

            bullish_count = 0
            bearish_count = 0
            total_words = 0

            for article in articles:
                text = (article.title + " " + article.content).lower()
                words = text.split()
                total_words += len(words)

                # Count keyword matches
                for keyword in self.bullish_keywords:
                    bullish_count += text.count(keyword.lower())

                for keyword in self.bearish_keywords:
                    bearish_count += text.count(keyword.lower())

            # Calculate sentiment score
            if total_words == 0:
                return {"sentiment": "neutral", "score": 0.0, "confidence": 0.0}

            bullish_ratio = bullish_count / total_words
            bearish_ratio = bearish_count / total_words

            net_sentiment = bullish_ratio - bearish_ratio

            # Normalize to -1 to +1 scale
            score = max(-1.0, min(1.0, net_sentiment * 100))

            # Determine sentiment category
            if score > 0.1:
                sentiment = "bullish"
            elif score < -0.1:
                sentiment = "bearish"
            else:
                sentiment = "neutral"

            # Calculate confidence based on keyword density
            keyword_density = (bullish_count + bearish_count) / total_words
            confidence = min(1.0, keyword_density * 50)  # Scale to 0-1

            return {
                "sentiment": sentiment,
                "score": score,
                "confidence": confidence,
                "method": "keyword_analysis",
                "bullish_signals": bullish_count,
                "bearish_signals": bearish_count
            }

        except Exception as e:
            self.logger.error(f"Error in keyword sentiment analysis: {e}")
            return {"sentiment": "neutral", "score": 0.0, "confidence": 0.0}

    async def _analyze_ai_sentiment(self, symbol: str, articles: List[NewsArticle]) -> Dict[str, Any]:
        """Analyze sentiment using AI/LLM"""
        try:
            if not articles:
                return {"sentiment": "neutral", "score": 0.0, "confidence": 0.0}

            # Prepare news summary for AI analysis
            news_summary = f"Recent news for {symbol}:\n\n"
            for i, article in enumerate(articles[:5]):  # Limit to top 5 articles
                news_summary += f"{i+1}. {article.title}\n"
                if article.content:
                    news_summary += f"   {article.content[:200]}...\n"
                news_summary += "\n"

            # Use LLM service for sentiment analysis
            result = await self.llm_service._analyze_news_sentiment(
                headline=f"News summary for {symbol}",
                symbol=symbol,
                content=news_summary
            )

            if result:
                return {
                    "sentiment": result.get("sentiment", "neutral"),
                    "score": result.get("score", 0.0),
                    "confidence": 0.8,  # High confidence for AI analysis
                    "method": "ai_analysis",
                    "reasoning": result.get("reasoning", "")
                }
            else:
                return {"sentiment": "neutral", "score": 0.0, "confidence": 0.0}

        except Exception as e:
            self.logger.error(f"Error in AI sentiment analysis: {e}")
            return {"sentiment": "neutral", "score": 0.0, "confidence": 0.0}

    async def _analyze_market_impact(self, symbol: str, articles: List[NewsArticle]) -> Dict[str, Any]:
        """Analyze potential market impact of news"""
        try:
            if not articles:
                return {"sentiment": "neutral", "score": 0.0, "confidence": 0.0}

            # High-impact keywords that typically move markets
            high_impact_bullish = [
                "earnings beat", "revenue growth", "acquisition", "merger", "partnership",
                "breakthrough", "approval", "expansion", "upgrade", "outperform"
            ]

            high_impact_bearish = [
                "earnings miss", "revenue decline", "lawsuit", "investigation", "downgrade",
                "bankruptcy", "scandal", "warning", "guidance cut", "underperform"
            ]

            impact_score = 0.0
            impact_count = 0

            for article in articles:
                text = (article.title + " " + article.content).lower()

                # Check for high-impact events
                for keyword in high_impact_bullish:
                    if keyword in text:
                        impact_score += 0.3
                        impact_count += 1

                for keyword in high_impact_bearish:
                    if keyword in text:
                        impact_score -= 0.3
                        impact_count += 1

            # Normalize score
            if impact_count > 0:
                score = max(-1.0, min(1.0, impact_score))
                confidence = min(1.0, impact_count * 0.2)
            else:
                score = 0.0
                confidence = 0.0

            # Determine sentiment
            if score > 0.1:
                sentiment = "bullish"
            elif score < -0.1:
                sentiment = "bearish"
            else:
                sentiment = "neutral"

            return {
                "sentiment": sentiment,
                "score": score,
                "confidence": confidence,
                "method": "market_impact_analysis",
                "impact_events": impact_count
            }

        except Exception as e:
            self.logger.error(f"Error in market impact analysis: {e}")
            return {"sentiment": "neutral", "score": 0.0, "confidence": 0.0}

    def _combine_sentiment_scores(self, sentiment_scores: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Combine multiple sentiment analysis results"""
        try:
            if not sentiment_scores:
                return {
                    "sentiment": "neutral",
                    "score": 0.0,
                    "magnitude": "low",
                    "confidence": 0.0,
                    "reasoning": "No sentiment data available"
                }

            # Weight different methods
            method_weights = {
                "keyword_analysis": 0.3,
                "ai_analysis": 0.5,
                "market_impact_analysis": 0.2
            }

            weighted_score = 0.0
            weighted_confidence = 0.0
            total_weight = 0.0

            reasoning_parts = []

            for result in sentiment_scores:
                method = result.get("method", "unknown")
                weight = method_weights.get(method, 0.1)

                weighted_score += result.get("score", 0.0) * weight
                weighted_confidence += result.get("confidence", 0.0) * weight
                total_weight += weight

                if result.get("reasoning"):
                    reasoning_parts.append(f"{method}: {result['reasoning']}")

            # Normalize
            if total_weight > 0:
                final_score = weighted_score / total_weight
                final_confidence = weighted_confidence / total_weight
            else:
                final_score = 0.0
                final_confidence = 0.0

            # Determine final sentiment
            if final_score > 0.2:
                sentiment = "bullish"
                magnitude = "high" if final_score > 0.5 else "medium"
            elif final_score < -0.2:
                sentiment = "bearish"
                magnitude = "high" if final_score < -0.5 else "medium"
            else:
                sentiment = "neutral"
                magnitude = "low"

            reasoning = "; ".join(reasoning_parts) if reasoning_parts else "Combined analysis of multiple sources"

            return {
                "sentiment": sentiment,
                "score": final_score,
                "magnitude": magnitude,
                "confidence": final_confidence,
                "reasoning": reasoning
            }

        except Exception as e:
            self.logger.error(f"Error combining sentiment scores: {e}")
            return {
                "sentiment": "neutral",
                "score": 0.0,
                "magnitude": "low",
                "confidence": 0.0,
                "reasoning": f"Error in analysis: {str(e)}"
            }

    def _get_trading_recommendation(self, sentiment: SentimentResult) -> str:
        """Get trading recommendation based on sentiment"""
        if sentiment.confidence < 0.3:
            return "HOLD - Low confidence in sentiment analysis"

        if sentiment.sentiment == "bullish":
            if sentiment.magnitude == "high":
                return "STRONG_BUY - High bullish sentiment with strong conviction"
            else:
                return "BUY - Moderate bullish sentiment"
        elif sentiment.sentiment == "bearish":
            if sentiment.magnitude == "high":
                return "STRONG_SELL - High bearish sentiment with strong conviction"
            else:
                return "SELL - Moderate bearish sentiment"
        else:
            return "HOLD - Neutral sentiment, wait for clearer signals"
