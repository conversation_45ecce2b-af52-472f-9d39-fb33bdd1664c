/**
 * Streamlined A.T.L.A.S Frontend - Single React Component
 * Consolidated interface with all 7 core features
 */

import React, { useState, useEffect, useRef } from 'react';
import { createChart } from 'lightweight-charts';

const API_BASE = 'http://localhost:8080/api/v1';

const AtlasApp = () => {
  // State management
  const [activeTab, setActiveTab] = useState('chat');
  const [chatMessages, setChatMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentSymbol, setCurrentSymbol] = useState('AAPL');
  const [quote, setQuote] = useState(null);
  const [scanResults, setScanResults] = useState({});
  const [portfolio, setPortfolio] = useState(null);
  const [positions, setPositions] = useState([]);
  
  // Chart reference
  const chartContainerRef = useRef();
  const chartRef = useRef();

  // Initialize chart
  useEffect(() => {
    if (activeTab === 'chart' && chartContainerRef.current && !chartRef.current) {
      chartRef.current = createChart(chartContainerRef.current, {
        width: chartContainerRef.current.clientWidth,
        height: 400,
        layout: {
          background: { color: '#1a1a1a' },
          textColor: '#d1d4dc',
        },
        grid: {
          vertLines: { color: '#2B2B43' },
          horzLines: { color: '#2B2B43' },
        },
        crosshair: {
          mode: 1,
        },
        rightPriceScale: {
          borderColor: '#485c7b',
        },
        timeScale: {
          borderColor: '#485c7b',
        },
      });

      const candlestickSeries = chartRef.current.addCandlestickSeries({
        upColor: '#4bffb5',
        downColor: '#ff4976',
        borderDownColor: '#ff4976',
        borderUpColor: '#4bffb5',
        wickDownColor: '#ff4976',
        wickUpColor: '#4bffb5',
      });

      // Load initial chart data
      loadChartData(currentSymbol);
    }

    return () => {
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }
    };
  }, [activeTab]);

  // Load real-time quote
  const loadQuote = async (symbol) => {
    try {
      const response = await fetch(`${API_BASE}/quote/${symbol}`);
      const data = await response.json();
      setQuote(data);
    } catch (error) {
      console.error('Error loading quote:', error);
    }
  };

  // Load chart data
  const loadChartData = async (symbol) => {
    try {
      const response = await fetch(`${API_BASE}/historical`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          symbol: symbol,
          timeframe: '1Day',
          limit: 100
        })
      });
      const data = await response.json();
      
      if (chartRef.current && data.length > 0) {
        const candlestickSeries = chartRef.current.addCandlestickSeries();
        const chartData = data.map(bar => ({
          time: new Date(bar.timestamp).getTime() / 1000,
          open: bar.open,
          high: bar.high,
          low: bar.low,
          close: bar.close
        }));
        candlestickSeries.setData(chartData);
      }
    } catch (error) {
      console.error('Error loading chart data:', error);
    }
  };

  // Send chat message
  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = { role: 'user', content: inputMessage, timestamp: new Date() };
    setChatMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      const response = await fetch(`${API_BASE}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: inputMessage,
          context: { symbol: currentSymbol }
        })
      });
      
      const data = await response.json();
      const assistantMessage = {
        role: 'assistant',
        content: data.response,
        timestamp: new Date(),
        type: data.type
      };
      
      setChatMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      setChatMessages(prev => [...prev, {
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date(),
        type: 'error'
      }]);
    } finally {
      setIsLoading(false);
      setInputMessage('');
    }
  };

  // Run technical scan
  const runScan = async (scanType) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE}/scan`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          scan_type: scanType,
          limit: 10
        })
      });
      
      const data = await response.json();
      setScanResults(prev => ({ ...prev, [scanType]: data }));
    } catch (error) {
      console.error('Error running scan:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load portfolio data
  const loadPortfolio = async () => {
    try {
      const response = await fetch(`${API_BASE}/portfolio`);
      const data = await response.json();
      setPortfolio(data.portfolio);
      setPositions(data.positions || []);
    } catch (error) {
      console.error('Error loading portfolio:', error);
    }
  };

  // Explain price movement
  const explainMovement = async (symbol) => {
    try {
      const response = await fetch(`${API_BASE}/explain/${symbol}`);
      const data = await response.json();
      
      const explanationMessage = {
        role: 'assistant',
        content: `📊 ${symbol} Movement Explanation:\n\n${data.explanation}`,
        timestamp: new Date(),
        type: 'explanation'
      };
      
      setChatMessages(prev => [...prev, explanationMessage]);
    } catch (error) {
      console.error('Error explaining movement:', error);
    }
  };

  // Auto-refresh data
  useEffect(() => {
    const interval = setInterval(() => {
      if (currentSymbol) {
        loadQuote(currentSymbol);
      }
      if (activeTab === 'portfolio') {
        loadPortfolio();
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [currentSymbol, activeTab]);

  // Initial data load
  useEffect(() => {
    loadQuote(currentSymbol);
    loadPortfolio();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      {/* Header */}
      <div className="bg-slate-800/50 backdrop-blur-sm border-b border-cyan-500/20 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
              A.T.L.A.S AI Trading System
            </h1>
            <p className="text-cyan-300/70 text-sm">Streamlined ChatGPT Trading Assistant</p>
          </div>
          
          {/* Symbol Input */}
          <div className="flex items-center space-x-4">
            <input
              type="text"
              value={currentSymbol}
              onChange={(e) => setCurrentSymbol(e.target.value.toUpperCase())}
              onBlur={() => loadQuote(currentSymbol)}
              className="bg-slate-700/50 border border-cyan-500/30 rounded px-3 py-1 text-white placeholder-gray-400 focus:border-cyan-400 focus:outline-none"
              placeholder="Symbol"
            />
            {quote && (
              <div className="text-right">
                <div className="text-lg font-semibold">${quote.price?.toFixed(2)}</div>
                <div className={`text-sm ${quote.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {quote.change >= 0 ? '+' : ''}{quote.change?.toFixed(2)} ({quote.change_percent?.toFixed(2)}%)
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-slate-800/30 border-b border-cyan-500/20">
        <div className="flex space-x-1 p-2">
          {[
            { id: 'chat', label: '💬 Chat', desc: 'AI Assistant' },
            { id: 'chart', label: '📈 Chart', desc: 'Live Charts' },
            { id: 'scanner', label: '🔍 Scanner', desc: 'Technical Scan' },
            { id: 'portfolio', label: '💼 Portfolio', desc: 'Positions' },
            { id: 'education', label: '📚 Education', desc: 'RAG Learning' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 rounded-lg transition-all ${
                activeTab === tab.id
                  ? 'bg-cyan-500/20 border border-cyan-400/50 text-cyan-300'
                  : 'bg-slate-700/30 border border-slate-600/30 text-gray-300 hover:bg-slate-600/30'
              }`}
            >
              <div className="text-sm font-medium">{tab.label}</div>
              <div className="text-xs opacity-70">{tab.desc}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6">
        {/* Chat Tab */}
        {activeTab === 'chat' && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-lg border border-cyan-500/20 h-96 mb-4 overflow-y-auto p-4">
              {chatMessages.map((msg, idx) => (
                <div key={idx} className={`mb-4 ${msg.role === 'user' ? 'text-right' : 'text-left'}`}>
                  <div className={`inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    msg.role === 'user'
                      ? 'bg-cyan-500/20 text-cyan-100'
                      : 'bg-slate-700/50 text-gray-100'
                  }`}>
                    <div className="whitespace-pre-wrap">{msg.content}</div>
                    <div className="text-xs opacity-50 mt-1">
                      {msg.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
              {isLoading && (
                <div className="text-center">
                  <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-400"></div>
                </div>
              )}
            </div>
            
            <div className="flex space-x-2">
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                placeholder="Ask about stocks, trading, or request analysis..."
                className="flex-1 bg-slate-700/50 border border-cyan-500/30 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:border-cyan-400 focus:outline-none"
              />
              <button
                onClick={sendMessage}
                disabled={isLoading}
                className="bg-cyan-500 hover:bg-cyan-600 disabled:opacity-50 px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Send
              </button>
            </div>
            
            {/* Quick Actions */}
            <div className="mt-4 flex flex-wrap gap-2">
              <button
                onClick={() => setInputMessage(`Analyze ${currentSymbol}`)}
                className="bg-slate-700/50 hover:bg-slate-600/50 px-3 py-1 rounded text-sm border border-slate-600/30"
              >
                Analyze {currentSymbol}
              </button>
              <button
                onClick={() => explainMovement(currentSymbol)}
                className="bg-slate-700/50 hover:bg-slate-600/50 px-3 py-1 rounded text-sm border border-slate-600/30"
              >
                Explain Movement
              </button>
              <button
                onClick={() => setInputMessage('What is RSI and how do I use it?')}
                className="bg-slate-700/50 hover:bg-slate-600/50 px-3 py-1 rounded text-sm border border-slate-600/30"
              >
                Learn RSI
              </button>
            </div>
          </div>
        )}

        {/* Chart Tab */}
        {activeTab === 'chart' && (
          <div className="max-w-6xl mx-auto">
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-lg border border-cyan-500/20 p-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">{currentSymbol} Chart</h2>
                <div className="flex space-x-2">
                  <button
                    onClick={() => loadChartData(currentSymbol)}
                    className="bg-cyan-500/20 hover:bg-cyan-500/30 px-3 py-1 rounded text-sm border border-cyan-400/30"
                  >
                    Refresh
                  </button>
                </div>
              </div>
              <div ref={chartContainerRef} className="w-full h-96 bg-slate-900/50 rounded"></div>
            </div>
          </div>
        )}

        {/* Scanner Tab */}
        {activeTab === 'scanner' && (
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              {[
                { type: 'oversold', label: 'Oversold (RSI < 30)', color: 'green' },
                { type: 'breakout', label: 'Breakout Patterns', color: 'blue' },
                { type: 'ttm_squeeze', label: 'TTM Squeeze', color: 'purple' },
                { type: 'macd_bullish', label: 'MACD Bullish', color: 'cyan' }
              ].map(scan => (
                <button
                  key={scan.type}
                  onClick={() => runScan(scan.type)}
                  className={`bg-${scan.color}-500/20 hover:bg-${scan.color}-500/30 border border-${scan.color}-400/30 rounded-lg p-4 text-center transition-colors`}
                >
                  <div className="font-medium">{scan.label}</div>
                  <div className="text-sm opacity-70 mt-1">
                    {scanResults[scan.type]?.length || 0} results
                  </div>
                </button>
              ))}
            </div>

            {/* Scan Results */}
            <div className="space-y-4">
              {Object.entries(scanResults).map(([scanType, results]) => (
                <div key={scanType} className="bg-slate-800/50 backdrop-blur-sm rounded-lg border border-cyan-500/20 p-4">
                  <h3 className="text-lg font-semibold mb-3 capitalize">{scanType.replace('_', ' ')} Results</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {results?.slice(0, 6).map((result, idx) => (
                      <div key={idx} className="bg-slate-700/30 rounded p-3 border border-slate-600/30">
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium text-cyan-300">{result.symbol}</span>
                          <span className="text-sm text-gray-400">Score: {result.score?.toFixed(1)}</span>
                        </div>
                        <div className="text-sm text-gray-300">${result.current_price?.toFixed(2)}</div>
                        <div className="text-xs text-gray-400 mt-1">{result.reasoning}</div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Portfolio Tab */}
        {activeTab === 'portfolio' && (
          <div className="max-w-6xl mx-auto space-y-6">
            {/* Portfolio Summary */}
            {portfolio && (
              <div className="bg-slate-800/50 backdrop-blur-sm rounded-lg border border-cyan-500/20 p-6">
                <h2 className="text-xl font-semibold mb-4">Portfolio Summary</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <div className="text-sm text-gray-400">Total Value</div>
                    <div className="text-lg font-semibold">${portfolio.total_value?.toFixed(2)}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Cash</div>
                    <div className="text-lg font-semibold">${portfolio.cash?.toFixed(2)}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Day P&L</div>
                    <div className={`text-lg font-semibold ${portfolio.day_pl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      ${portfolio.day_pl?.toFixed(2)}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Positions</div>
                    <div className="text-lg font-semibold">{portfolio.positions_count}</div>
                  </div>
                </div>
              </div>
            )}

            {/* Positions */}
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-lg border border-cyan-500/20 p-6">
              <h2 className="text-xl font-semibold mb-4">Current Positions</h2>
              {positions.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-slate-600/30">
                        <th className="text-left py-2">Symbol</th>
                        <th className="text-right py-2">Qty</th>
                        <th className="text-right py-2">Price</th>
                        <th className="text-right py-2">Market Value</th>
                        <th className="text-right py-2">P&L</th>
                      </tr>
                    </thead>
                    <tbody>
                      {positions.map((pos, idx) => (
                        <tr key={idx} className="border-b border-slate-700/30">
                          <td className="py-2 font-medium text-cyan-300">{pos.symbol}</td>
                          <td className="text-right py-2">{pos.qty}</td>
                          <td className="text-right py-2">${pos.current_price?.toFixed(2)}</td>
                          <td className="text-right py-2">${pos.market_value?.toFixed(2)}</td>
                          <td className={`text-right py-2 ${pos.unrealized_pl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            ${pos.unrealized_pl?.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center text-gray-400 py-8">No positions found</div>
              )}
            </div>
          </div>
        )}

        {/* Education Tab */}
        {activeTab === 'education' && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-lg border border-cyan-500/20 p-6">
              <h2 className="text-xl font-semibold mb-4">📚 Trading Education (RAG System)</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <button
                  onClick={() => setInputMessage('What does Trading in the Zone say about psychology?')}
                  className="bg-slate-700/50 hover:bg-slate-600/50 p-4 rounded-lg border border-slate-600/30 text-left"
                >
                  <div className="font-medium">Trading Psychology</div>
                  <div className="text-sm text-gray-400 mt-1">Learn from Trading in the Zone</div>
                </button>
                
                <button
                  onClick={() => setInputMessage('Explain the CAN SLIM system from How to Make Money in Stocks')}
                  className="bg-slate-700/50 hover:bg-slate-600/50 p-4 rounded-lg border border-slate-600/30 text-left"
                >
                  <div className="font-medium">CAN SLIM System</div>
                  <div className="text-sm text-gray-400 mt-1">Growth stock selection</div>
                </button>
                
                <button
                  onClick={() => setInputMessage('What do Market Wizards say about risk management?')}
                  className="bg-slate-700/50 hover:bg-slate-600/50 p-4 rounded-lg border border-slate-600/30 text-left"
                >
                  <div className="font-medium">Risk Management</div>
                  <div className="text-sm text-gray-400 mt-1">Wisdom from Market Wizards</div>
                </button>
                
                <button
                  onClick={() => setInputMessage('Explain technical analysis patterns from Martin Pring')}
                  className="bg-slate-700/50 hover:bg-slate-600/50 p-4 rounded-lg border border-slate-600/30 text-left"
                >
                  <div className="font-medium">Chart Patterns</div>
                  <div className="text-sm text-gray-400 mt-1">Technical Analysis Explained</div>
                </button>
              </div>
              
              <div className="text-sm text-gray-400">
                💡 Ask questions about trading concepts, and I'll search through the knowledge from top trading books to provide educational answers.
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AtlasApp;
